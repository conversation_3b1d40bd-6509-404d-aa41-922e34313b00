{"name": "Historic Zoom Recordings Transcription 1 - SalesDemoAgent", "flow": [{"id": 5, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"ca": "", "qs": [{"name": "from", "value": "2025-03-01"}, {"name": "to", "value": "2025-03-12"}, {"name": "page_size", "value": "200"}], "url": "https://api.zoom.us/v2/accounts/me/recordings", "data": "", "gzip": true, "method": "get", "headers": [{"name": "Authorization", "value": "Bearer eyJzdiI6IjAwMDAwMiIsImFsZyI6IkhTNTEyIiwidiI6IjIuMCIsImtpZCI6Ijk3MjkwMzFiLWRjYjgtNDUxYi04OTI2LTA1ZjRjN2Y0YzFlMCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fNdZBsupbklChIuNf8jFC-JdvD3BuAeA4qdxv6jFnHL6VFWNUbhrSb-GM10GkxVA1wy57w1rDQ7AWz9Y-kN7lA"}], "timeout": "", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "raw", "contentType": "application/json", "serializeUrl": false, "shareCookies": false, "parseResponse": true, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": 0, "y": 0}, "restore": {"expect": {"qs": {"mode": "chose", "items": [null, null, null]}, "method": {"mode": "chose", "label": "GET"}, "headers": {"mode": "chose", "items": [null]}, "bodyType": {"label": "Raw"}, "contentType": {"label": "JSON (application/json)"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}, {"id": 7, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{5.data.meetings}}"}, "metadata": {"designer": {"x": 300, "y": 0}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 8, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{7.recording_files}}"}, "metadata": {"designer": {"x": 600, "y": 0}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 6, "module": "google-sheets:addRow", "version": 2, "parameters": {"__IMTCONN__": 2160110}, "filter": {"name": "Only audio", "conditions": [[{"a": "{{8.file_type}}", "b": "M4A", "o": "text:equal"}]]}, "mapper": {"from": "drive", "mode": "select", "values": {"0": "{{7.topic}}", "1": "{{7.id}}", "2": "{{7.start_time}}", "3": "{{8.id}}", "4": "{{8.recording_type}}", "5": "{{8.file_type}}", "6": "{{8.meeting_id}}", "7": "{{8.play_url}}", "8": "{{8.download_url}}", "9": "{{7.duration}}", "10": "{{7.host_email}}"}, "sheetId": "Sheet1", "spreadsheetId": "/1cTrgbFekSsPfQ_crGsMfpz3uVFRudSn0aqMAx6pfOCA", "includesHeaders": true, "insertDataOption": "INSERT_ROWS", "valueInputOption": "USER_ENTERED", "insertUnformatted": false}, "metadata": {"designer": {"x": 975, "y": -12}, "restore": {"expect": {"from": {"label": "My Drive"}, "mode": {"label": "Search by path"}, "sheetId": {"label": "Sheet1"}, "spreadsheetId": {"path": ["Zoom recordings"]}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}], "type": "collection", "label": "Values"}]}, "insertDataOption": {"mode": "chose", "label": "Insert rows"}, "valueInputOption": {"mode": "chose", "label": "User entered"}, "insertUnformatted": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "insertUnformatted", "type": "boolean", "label": "Unformatted", "required": true}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "insertDataOption", "type": "select", "label": "Insert data option", "validate": {"enum": ["INSERT_ROWS", "OVERWRITE"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "(L)"}, {"name": "12", "type": "text", "label": "(M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}], "type": "collection", "label": "Values"}]}}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "eu2.make.com", "notes": []}}