#!/usr/bin/env python3
import requests
import base64
import json

# YOUR ZOOM APP CREDENTIALS
CLIENT_ID = "gSUHotjR5G89Lo7Y91l9Q"
CLIENT_SECRET = "eJ7xowTFogFILrm9i8ocGxZSL4W3c97w"
ACCOUNT_ID = "FGcAlREpQgGfY8G9wM6qsw"

def get_zoom_token():
    """Generate Zoom OAuth access token"""
    
    # Create authentication string
    auth_string = f"{CLIENT_ID}:{CLIENT_SECRET}"
    auth_b64 = base64.b64encode(auth_string.encode()).decode()
    
    # Request token
    response = requests.post(
        'https://zoom.us/oauth/token',
        headers={
            'Authorization': f'Basic {auth_b64}',
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        data={
            'grant_type': 'account_credentials',
            'account_id': ACCOUNT_ID
        }
    )
    
    if response.status_code == 200:
        token_data = response.json()
        access_token = token_data['access_token']
        
        print("🎉 SUCCESS! Access token generated:")
        print(f"Bearer {access_token}")
        print()
        print("📋 FOR MAKE.COM:")
        print("1. Copy the token above")
        print("2. Update Make.com Automation 1 HTTP Authorization header")
        print("3. Set date range: from=2025-03-13 to=2025-07-23")
        print("4. Run the automation")
        
        return access_token
    else:
        print(f"❌ Error: {response.status_code}")
        print(f"Response: {response.text}")
        return None

if __name__ == "__main__":
    get_zoom_token() 