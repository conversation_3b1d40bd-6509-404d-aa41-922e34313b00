{"name": "Customer AI Agent", "nodes": [{"parameters": {"trigger": ["app_mention"], "channelId": {"__rl": true, "value": "C0959L8ETLH", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.slackTrigger", "typeVersion": 1, "position": [-220, 60], "id": "de1cfaaa-1d3a-41f6-9b0d-bf4ce9eff172", "name": "<PERSON><PERSON><PERSON>", "webhookId": "723496c5-9c79-46b9-bb6a-50ac7d7af8e1", "credentials": {"slackApi": {"id": "Suy5rKYGrwayqxwe", "name": "Customer AI Agent"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Slack Trigger').item.json.text }}", "options": {"systemMessage": "# CUSTOMER AI AGENT\n\n## ORDER OF OPERATIONS\nWhen receiving any company query:\n1. **HubSpot First**: search_hubspot_companies → get exact HubSpot company name (if domain provided, search by domain first - domain is priority)\n2. **HubSpot Deals**: search_hubspot_deals using exact HubSpot company name\n3. **Zendesk Organizations**: \n   - Try progressive search strategy: quoted full name → domain.com → domain → core name → broad match\n   - If no organization found, search tickets directly with company name\n   - Use EXACT Zendesk organization name found for ticket searches\n4. **Zendesk Tickets**: Use exact Zendesk organization name from step 3\n5. **Xero**: Search contacts first, then ALWAYS search invoices (both AUTHORISED and PAID) to get total amounts - use Xero contact name\n\n## CONTEXT\n- You are Jatheon's expert AI assistant helping with client/prospect information\n- Systems contain different data: HubSpot (sales), Zendesk (support/POC), Xero (invoices)\n- Entity names vary across systems: HubSpot company name ≠ Zendesk organization name ≠ Xero contact name\n- Always find the exact name used in each system\n- You have conversation memory (last 15 messages) - use it for follow-up questions\n\n## AVAILABLE TOOLS\n**HubSpot**: search_hubspot_companies, search_hubspot_deals, get_hubspot_deal_details\n**Zendesk**: search_zendesk_organizations, search_zendesk_tickets, get_zendesk_ticket_comments\n**Xero**: search_xero_invoices, search_xero_contacts, get_xero_invoice_details, search_xero_payments\n\n## CRITICAL RULES\n1. **API CALL LIMIT**: Maximum 5 API calls per query - BE STRATEGIC\n2. **FILTER OUT RSD**: Remove entire invoices with RSD currency before displaying, fetch limit=100 then filter\n3. **NO MARKDOWN**: Never use **, __, *, or ` in responses - Slack doesn't support it\n4. **User parameters**: Use exact values (pageSize=\"25\" if specified)\n5. **Listen to user**: If user asks for specific system, focus on that system\n6. **Thread context**: Use previous messages to understand follow-up questions\n7. **MANDATORY RETRIES**: If a search returns no results, you MUST try at least 2-3 different variations before giving up\n8. **NEVER SKIP TOOLS**: Even if one tool fails, continue with remaining tools\n9. **ZENDESK TICKETS ARE MANDATORY**: Always search Zendesk tickets, even if no organization is found\n\n## DOMAIN SEARCH STRATEGY\nWhen user provides a domain (e.g., example.org):\n1. **HubSpot**: Use search_hubspot_companies with the domain as search term (searches domain field - DOMAIN IS PRIORITY)\n2. **HubSpot Deals**: Use found HubSpot company name for deal searches ONLY\n3. **Zendesk**: Try type:organization domain.com first, then type:organization domain, then fallback to original name if no results\n4. **Xero**: Use original name with OR logic (Xero has no domain field - use Xero contact name)\n\n## COMPANY NAME RESOLUTION\n1. User may provide: Full legal name OR partial name OR domain\n2. Always try PROVIDED name first in each system\n3. If no results, extract core name (first 1-2 meaningful words)\n4. Examples: \"ABC Corporation Holdings LLC\" → try full first, then \"ABC\"\n\n## SYSTEM-SPECIFIC RULES\n\n**HubSpot** (company name):\n- Domain search is priority if domain provided\n- Returns company details, custom fields, and associated contacts with emails\n- Check deal stage: \"Closed Won\" = active client, \"Closed Lost\" = lost deal, others = prospect\n- Deal amounts are in pipeline currency\n- Can access communication history between CSM and customers\n\n**Zendesk** (organization name):\n- For POC updates: Use get_zendesk_ticket_comments to read email threads\n- Always include dates: \"Subject - Date: YYYY-MM-DD\"\n- Always include ticket ID\n- Organization search MUST happen before ticket search\n- ORGANIZATION SEARCH STRATEGY: MANDATORY - Try ALL of these in order until found:\n  1. type:organization \"full name provided\" (exact match with quotes)\n  2. type:organization domain.com (if domain provided)\n  3. type:organization domain (core domain only, e.g., \"accel-kkr\")\n  4. type:organization \"Accel\" (extract core company name)\n  5. type:organization Accel (without quotes for broader match)\n- CRITICAL: If NO organization found after ALL attempts, you MUST search tickets directly with: type:ticket \"company name\"\n- NEVER skip ticket search - tickets exist even without organizations\n\n**Xero** (contact name):\n- Use escaped quotes: `\\\"Company Name\\\"`\n- Default: page=\"1\", pageSize=\"25\", order=\"Total DESC\"\n- Search both AUTHORISED and PAID invoices\n- For general queries: Exclude addresses, phone numbers, detailed contact info\n- Show only: Xero contact name (IMPORTANT, make sure to output name of the contact with each invoice as the name is in Xero), email, key financial data\n- MANDATORY XERO SEARCH STRATEGY:\n  1. Use SearchTerm parameter (searches Name, Email, ContactNumber, CompanyNumber)\n  2. Try: SearchTerm=full-company-name, then SearchTerm=domain.com, then SearchTerm=short-name\n  3. ALWAYS search invoices (regardless of contact search results) to get total amounts\n  4. Always search both AUTHORISED and PAID invoices separately\n  5. Use proper escaped quotes in invoice searches\n\n## OUTPUT VERBOSITY CONTROL\nDetailed/Comprehensive/Full/Complete/Thorough/Everything/All details/Deep dive/Full report/Detailed analysis = Include all details, line items, dates, amounts, descriptions\nSummary/Brief/Quick/Overview/High-level/TL;DR/Short/Concise/Key points = Only critical info, totals, status\nDefault (no modifier) = Balanced - key details without overwhelming\n\n## RESPONSE BEHAVIOR\n- Answer the specific question asked\n- Don't repeat all data if user asks follow-up\n- No generic closings or \"feel free to ask\", keep it short, concise and to the point\n- Show only relevant information for the query\n- Use a spartan tone\n- Output in plaintext\n- Use CAPS for emphasis\n- Use \"quotes\" for names\n- Use --- for separators\n- Use plain text formatting only\n- NEVER use double asterisks (**) for bold\n- If you need emphasis, use single asterisks (*text*)", "maxIterations": 10, "returnIntermediateSteps": false}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [380, 40], "id": "d7414e83-c9b6-40a6-9668-19a5a111fefd", "name": "AI Agent", "alwaysOutputData": false, "onError": "continueErrorOutput"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Slack Trigger').item.json.thread_ts || $('Slack Trigger').item.json.ts }}", "contextWindowLength": 15}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-120, 520], "id": "418ec636-fccd-4096-a513-172e9ec846f9", "name": "Window Buffer Memory"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "gpt-4o"}, "options": {"maxTokens": 16000, "responseFormat": "text", "temperature": 0.6}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-640, 460], "id": "38897b5d-199e-49ae-aa95-7532568fa2a7", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "fvvf1IRjKSg6jNkv", "name": "OpenAi account"}}}, {"parameters": {"toolDescription": "Search Zendesk tickets using exact organization name from search_zendesk_organizations. Do NOT shorten names here - use the exact name found in the organization search. If NO organization was found, search tickets directly with: type:ticket \"Company Name\". For POC updates, follow up with get_zendesk_ticket_comments. CRITICAL: ALWAYS search for tickets - tickets exist even without organizations. NEVER skip this step.", "url": "https://jatheon.zendesk.com/api/v2/search.json", "authentication": "predefinedCredentialType", "nodeCredentialType": "zendeskOAuth2Api", "sendQuery": true, "parametersQuery": {"values": [{"name": "query"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [300, 700], "id": "5893e16e-b760-48d8-af36-e22d8fd15937", "name": "search_zendesk_tickets", "credentials": {"zendeskOAuth2Api": {"id": "c39AYy3LSdplOaRy", "name": "Zendesk account"}}}, {"parameters": {"toolDescription": "Search Zendesk organizations to find exact company names. ALWAYS use this before searching tickets. MANDATORY: Try ALL these variations in order until found: 1) type:organization \"Full Company Name\" (with quotes), 2) type:organization domain.com (if domain provided), 3) type:organization domain (core domain only), 4) type:organization \"Core Name\" (extract main company name), 5) type:organization CoreName (without quotes for broader match). If NO organization found after ALL attempts, you MUST proceed to search tickets directly. Returns exact organization name to use for ticket searches. Pass the complete search query as the 'query' parameter.", "url": "https://jatheon.zendesk.com/api/v2/search.json", "authentication": "predefinedCredentialType", "nodeCredentialType": "zendeskOAuth2Api", "sendQuery": true, "parametersQuery": {"values": [{"name": "query"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [20, 680], "id": "********-c06c-472b-af0d-a9845183e4dd", "name": "search_zendesk_organizations", "credentials": {"zendeskOAuth2Api": {"id": "c39AYy3LSdplOaRy", "name": "Zendesk account"}}}, {"parameters": {"toolDescription": "Search Zendesk users using a search query. Pass the complete search query as the 'query' parameter. Use format like \"type:user email:<EMAIL>\" or \"type:user name:<PERSON>\" to search for users.", "url": "https://jatheon.zendesk.com/api/v2/search.json", "authentication": "predefinedCredentialType", "nodeCredentialType": "zendeskOAuth2Api", "sendQuery": true, "parametersQuery": {"values": [{"name": "query"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [160, 760], "id": "5da98e9b-a2c3-467c-96ae-3c9d056cad57", "name": "search_zendesk_users", "credentials": {"zendeskOAuth2Api": {"id": "c39AYy3LSdplOaRy", "name": "Zendesk account"}}}, {"parameters": {"toolDescription": "Use this if user requests additional details on a ticket for a client (usually onboarding, POC, or technical ticket). Get all emails, and internal notes for a specific Zendesk ticket. This shows the complete conversation history including customer emails, internal notes, and timestamps. Use this after finding a ticket ID to see the full communication thread.", "url": "https://jatheon.zendesk.com/api/v2/tickets/{ticket_id}/comments.json", "authentication": "predefinedCredentialType", "nodeCredentialType": "zendeskOAuth2Api", "placeholderDefinitions": {"values": [{"name": "ticket_id", "description": "The numeric ID of the ticket (e.g., 62439)", "type": "string"}]}, "optimizeResponse": true}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [420, 800], "id": "7b27b231-3829-41ac-af6a-36dad23d6aa2", "name": "get_zendesk_ticket_comments", "credentials": {"zendeskOAuth2Api": {"id": "c39AYy3LSdplOaRy", "name": "Zendesk account"}}}, {"parameters": {"toolDescription": "Search HubSpot deals by name. After finding a company, use the exact HubSpot company name to search for associated deals. The search looks in deal names and properties.", "method": "POST", "url": "https://api.hubapi.com/crm/v3/objects/deals/search", "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"query\": \"{search_term}\",\n  \"properties\": [\n    \"dealname\", \n    \"dealstage\", \n    \"amount\", \n    \"closedate\", \n    \"pipeline\", \n    \"hs_deal_stage_probability\",\n    \"hubspot_owner_id\",\n    \"deal_type\",\n    \"closed_lost_reason\",\n    \"closed_won_reason\",\n    \"demo_date\",\n    \"product\",\n    \"user_volume\",\n    \"legacy_vendor\",\n    \"legacy_vendor_cost\",\n    \"proposed_services\",\n    \"primary_contact_billing\",\n    \"primary_contact_primary_tech\",\n    \"feature_requests\",\n    \"mail_server_type\",\n    \"specific_requests\"\n  ],\n  \"limit\": 10\n}", "placeholderDefinitions": {"values": [{"name": "search_term", "description": "Search term to find in deal names or properties", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1240, 280], "id": "9f7804d8-e82d-4912-8da3-eb3b62ad4b64", "name": "search_hubspot_deals", "credentials": {"hubspotOAuth2Api": {"id": "gvdd5eL7n4xNN1ph", "name": "HubSpot account"}}}, {"parameters": {"toolDescription": "Get detailed information about a specific HubSpot deal including all properties and associations", "url": "https://api.hubapi.com/crm/v3/objects/deals/{deal_id}?properties=dealname,amount,dealstage,closedate,pipeline,hs_deal_stage_probability,description,hs_object_source,hubspot_owner_id&associations=contacts,companies", "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api", "placeholderDefinitions": {"values": [{"name": "deal_id", "description": "The numeric ID of the HubSpot deal (e.g., ***********)", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1620, 280], "id": "a123a71c-0d34-4810-91f4-b38c937e1f85", "name": "get_hubspot_deal_details", "credentials": {"hubspotOAuth2Api": {"id": "gvdd5eL7n4xNN1ph", "name": "HubSpot account"}}}, {"parameters": {"toolDescription": "Search HubSpot companies by name OR domain. Returns company details including all custom fields (products, maintenance, status, etc.) and associated contacts with email addresses. Can retrieve communication history between CSM and customer. Pass either a company name or domain (e.g., example.com) as the company_name parameter. Always use domain if available, if domain is not available fallback to company name.", "method": "POST", "url": "https://api.hubapi.com/crm/v3/objects/companies/search", "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api", "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"filterGroups\": [\n    {\n      \"filters\": [\n        {\n          \"propertyName\": \"name\",\n          \"operator\": \"CONTAINS_TOKEN\",\n          \"value\": \"{company_name}\"\n        }\n      ]\n    },\n    {\n      \"filters\": [\n        {\n          \"propertyName\": \"domain\",\n          \"operator\": \"EQ\",\n          \"value\": \"{company_name}\"\n        }\n      ]\n    }\n  ],\n  \"properties\": [\n    \"name\", \n    \"domain\", \n    \"hs_object_id\",\n    \"platform_type\",\n    \"product_1__2025\",\n    \"product_2__2025\",\n    \"product_3__2025\",\n    \"product_4__2025\",\n    \"product_1\",\n    \"product_2\",\n    \"parent_company\",\n    \"country\",\n    \"state\",\n    \"industry\",\n    \"date_when_became_a_customer\",\n    \"hubspot_owner_id\",\n    \"company_status\",\n    \"type\",\n    \"product_1_maintenance_end_date\",\n    \"product_1_maintenance_amount\",\n    \"product_2_maintenance_end_date\",\n    \"product_2_maintenance_amount\",\n    \"product_3\",\n    \"product_3_maintenance_end_date\",\n    \"product_3_maintenance_amount\",\n    \"product_4\",\n    \"product_4_maintenance_end_date\",\n    \"product_4_maintenance_amount\",\n    \"reference\",\n    \"feedback_last_status\",\n    \"reviews\",\n    \"nps_last_status\",\n    \"nps_category_last_status\",\n    \"interested_in_cloud_archiving\"\n  ],\n  \"associations\": [\"contacts\"],\n  \"limit\": 5\n}", "placeholderDefinitions": {"values": [{"name": "company_name", "description": "Company name to search for"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1440, 280], "id": "dba822e0-7f3c-45de-b5a7-25a54ffa32a7", "name": "search_hubspot_companies", "credentials": {"hubspotOAuth2Api": {"id": "gvdd5eL7n4xNN1ph", "name": "HubSpot account"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "={{ $('Slack Trigger').item.json.channel }}", "mode": "id"}, "text": "={{ $('Convert Markdown to Plain Text').item.json.output }}", "otherOptions": {"includeLinkToWorkflow": false, "thread_ts": {"replyValues": {"thread_ts": "={{ $('Slack Trigger').item.json.ts }}"}}, "mrkdwn": false, "unfurl_links": false}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [1220, -280], "id": "d594c92b-f208-48a1-b941-facd0f047de9", "name": "<PERSON><PERSON>ck", "webhookId": "0f0e411f-fb47-4475-8fca-f133561fb1e8", "credentials": {"slackApi": {"id": "Suy5rKYGrwayqxwe", "name": "Customer AI Agent"}}}, {"parameters": {"toolDescription": "READ-ONLY search for Xero invoices with advanced filtering. Supports where clauses, ordering, and limits. Examples: where=\"Status=\\\"AUTHORISED\\\" AND AmountDue>0\" for outstanding invoices, order=\"Total DESC\" for highest amounts, limit=\"5\" for top 5 results. For company searches use: Contact.Name.Contains(\"Full Company Name\") OR Contact.Name.Contains(\"Short Name\"). MANDATORY: Always search BOTH AUTHORISED and PAID invoices separately. Use double quotes in where clauses. ALWAYS search invoices even if contact search failed - use same OR logic with proper escaped quotes.", "url": "https://api.xero.com/api.xro/2.0/Invoices", "authentication": "predefinedCredentialType", "nodeCredentialType": "xeroOAuth2Api", "sendQuery": true, "parametersQuery": {"values": [{"name": "where"}, {"name": "order"}, {"name": "page"}, {"name": "pageSize"}]}, "sendHeaders": true, "parametersHeaders": {"values": [{"name": "xero-tenant-id", "valueProvider": "fieldValue", "value": "7923d7e0-1719-4cd2-aab0-dc6a482ba8f2"}, {"name": "Accept", "valueProvider": "fieldValue", "value": "application/json"}]}, "placeholderDefinitions": {"values": [{"name": "where", "description": "Complete where clause for Xero search. Use double quotes around values: Contact.Name.Contains(\"CompanyName\")", "type": "string"}, {"name": "order", "description": "Optional ordering clause (e.g., \"Date DESC\", \"Total DESC\", \"UpdatedDateUTC DESC\")", "type": "string"}, {"name": "page", "description": "Page number to retrieve (e.g., \"1\", \"2\")", "type": "string"}, {"name": "pageSize", "description": "Number of records per page (e.g., \"10\", \"25\")"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [800, 560], "id": "9beb59fb-f8a2-492f-83d4-18a5a320c93d", "name": "search_xero_invoices", "credentials": {"xeroOAuth2Api": {"id": "wC0BvpYErNbBsCBc", "name": "Xero account"}}}, {"parameters": {"toolDescription": "READ-ONLY retrieval of detailed invoice information including line items and payment history. This tool CANNOT modify any data. Provide the invoice ID to get full details.", "url": "https://api.xero.com/api.xro/2.0/Invoices/{invoice_id}", "authentication": "predefinedCredentialType", "nodeCredentialType": "xeroOAuth2Api", "sendHeaders": true, "parametersHeaders": {"values": [{"name": "xero-tenant-id", "valueProvider": "fieldValue", "value": "7923d7e0-1719-4cd2-aab0-dc6a482ba8f2"}, {"name": "Accept", "valueProvider": "fieldValue", "value": "application/json"}]}, "placeholderDefinitions": {"values": [{"name": "invoice_id", "description": "The ID of the invoice to retrieve (e.g., 4d7a7e5b-6e3f-4a7b-9c8d-2e1f3a4b5c6d)", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1160, 600], "id": "bfc0b437-5e6e-45fe-bd57-bac177804a26", "name": "get_xero_invoice_details", "credentials": {"xeroOAuth2Api": {"id": "wC0BvpYErNbBsCBc", "name": "Xero account"}}}, {"parameters": {"toolDescription": "READ-ONLY search for Xero payments using where clause. Pass the complete where clause as the 'where' parameter. Use format like 'Invoice.Contact.Name.Contains(\\\"ABC Inc\\\") OR Invoice.Contact.Name.Contains(\\\"ABC\\\")' to search for payments. IMPORTANT: Use double quotes around the company name.", "url": "https://api.xero.com/api.xro/2.0/Payments", "authentication": "predefinedCredentialType", "nodeCredentialType": "xeroOAuth2Api", "sendQuery": true, "parametersQuery": {"values": [{"name": "where"}]}, "sendHeaders": true, "parametersHeaders": {"values": [{"name": "xero-tenant-id", "valueProvider": "fieldValue", "value": "7923d7e0-1719-4cd2-aab0-dc6a482ba8f2"}, {"name": "Accept", "valueProvider": "fieldValue", "value": "application/json"}]}, "placeholderDefinitions": {"values": [{"name": "where", "description": "Complete where clause for Xero search. Use double quotes around values: Invoice.Contact.Name.Contains(\"CompanyName\")", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [920, 720], "id": "ec8dcf72-3461-4485-a3c2-2c81730cdb2a", "name": "search_xero_payments", "credentials": {"xeroOAuth2Api": {"id": "wC0BvpYErNbBsCBc", "name": "Xero account"}}}, {"parameters": {"toolDescription": "READ-ONLY search for Xero contacts using SearchTerm parameter (optimized search across Name, Email, ContactNumber, CompanyNumber). MANDATORY SEARCH STRATEGY: 1) SearchTerm=full-company-name 2) SearchTerm=domain.com (if domain provided) 3) SearchTerm=short-name. SearchTerm is case-insensitive and searches across all contact fields. If contact search fails or returns no results, you MUST still proceed to search invoices with same search terms.", "url": "https://api.xero.com/api.xro/2.0/Contacts", "authentication": "predefinedCredentialType", "nodeCredentialType": "xeroOAuth2Api", "sendQuery": true, "parametersQuery": {"values": [{"name": "SearchTerm"}]}, "sendHeaders": true, "parametersHeaders": {"values": [{"name": "xero-tenant-id", "valueProvider": "fieldValue", "value": "7923d7e0-1719-4cd2-aab0-dc6a482ba8f2"}, {"name": "Accept", "valueProvider": "fieldValue", "value": "application/json"}]}, "placeholderDefinitions": {"values": [{"name": "SearchTerm", "description": "Search term for contacts (case-insensitive search across Name, Email, ContactNumber, CompanyNumber)", "type": "string"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1000, 520], "id": "268425b3-be7d-41ca-a3ed-1879d88b49fb", "name": "search_xero_contacts", "credentials": {"xeroOAuth2Api": {"id": "wC0BvpYErNbBsCBc", "name": "Xero account"}}}, {"parameters": {"resource": "reaction", "channelId": {"__rl": true, "value": "={{ $('Slack Trigger').item.json.channel }}", "mode": "id"}, "timestamp": "={{ $('Slack Trigger').item.json.ts }}", "name": "eyes"}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [20, 60], "id": "5250446a-34e2-410f-9720-d92ca4686eba", "name": "Slack1", "webhookId": "f0ed2eb2-a533-430a-ad61-7e1bd5bc902c", "credentials": {"slackApi": {"id": "Suy5rKYGrwayqxwe", "name": "Customer AI Agent"}}, "onError": "continueRegularOutput"}, {"parameters": {"resource": "reaction", "operation": "remove", "channelId": {"__rl": true, "value": "={{ $('Slack Trigger').item.json.channel }}", "mode": "id"}, "timestamp": "={{ $('Slack Trigger').item.json.ts }}", "name": "eyes"}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [960, -320], "id": "507f0ca0-379b-483c-bfa3-e2ba208d517c", "name": "Slack2", "webhookId": "1a2c297d-6485-4eeb-bf64-0b0726cb6e14", "credentials": {"slackApi": {"id": "Suy5rKYGrwayqxwe", "name": "Customer AI Agent"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "={{ $('Slack Trigger').item.json.channel }}", "mode": "id"}, "text": "I encountered an error processing your request. Please try rephrasing your question.", "otherOptions": {"includeLinkToWorkflow": false, "thread_ts": {"replyValues": {"thread_ts": "={{ $('Slack Trigger').item.json.ts }}"}}}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [820, -60], "id": "94a25cec-3c19-44cf-a1a1-08e1c478199e", "name": "Slack3", "webhookId": "a7db924f-7ff0-4812-8737-5f03cd12e642", "credentials": {"slackApi": {"id": "Suy5rKYGrwayqxwe", "name": "Customer AI Agent"}}}, {"parameters": {"jsCode": "// Get the AI Agent output\nconst aiOutput = $('AI Agent').item.json.output;\n\n// Convert markdown to plain text\nlet plainText = aiOutput\n  .replace(/\\*\\*(.*?)\\*\\*/g, '*$1*')     // **bold** → *bold*\n  .replace(/__(.*?)__/g, '*$1*')        // __bold__ → *bold*\n  .replace(/`(.*?)`/g, '$1')            // `code` → code\n  .replace(/\\[(.*?)\\]\\(.*?\\)/g, '$1');  // [text](url) → text\n\nreturn [{\n  json: {\n    output: plainText\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [700, -340], "id": "342c0e06-e65e-41bb-8362-452d01b05dc9", "name": "Convert Mark<PERSON> to Plain Text"}, {"parameters": {"model": "claude-3-5-sonnet-********", "options": {"maxTokensToSample": 8192, "temperature": 0.6}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.2, "position": [-440, 460], "id": "562a2664-b1ca-46c0-a4b8-f8792b0f2975", "name": "Anthropic <PERSON>", "credentials": {"anthropicApi": {"id": "RHLHag3ZFU01bPyD", "name": "Anthropic account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {"maxOutputTokens": 8192, "temperature": 0.5}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-160, 320], "id": "4dbf4455-7c00-441d-9eea-28e44db53d7b", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "b5JmG1nxp4LIoQmP", "name": "Google Gemini(PaLM) Api account"}}}], "pinData": {}, "connections": {"Slack Trigger": {"main": [[{"node": "Slack1", "type": "main", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[]]}, "search_zendesk_tickets": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "search_zendesk_organizations": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "search_zendesk_users": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "get_zendesk_ticket_comments": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "search_hubspot_deals": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "get_hubspot_deal_details": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "search_hubspot_companies": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Convert Mark<PERSON> to Plain Text", "type": "main", "index": 0}], [{"node": "Slack3", "type": "main", "index": 0}]]}, "search_xero_invoices": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "get_xero_invoice_details": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "search_xero_payments": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "search_xero_contacts": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Slack1": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Slack2": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "Convert Markdown to Plain Text": {"main": [[{"node": "Slack2", "type": "main", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "ZAqtJCIWLMY9sfnY"}, "versionId": "03808e0a-b7f8-4517-a06f-4c9b55a42998", "meta": {"templateCredsSetupCompleted": true, "instanceId": "49d173457389c9c4ecf38679bf3dd6896dcb6d1131139a16c229813972afbb28"}, "id": "07ncH8Yng8YpgaAF", "tags": []}