{"name": "Create message based on transcript 3 - SalesDemoAgent", "flow": [{"id": 5, "module": "google-sheets:filterRows", "version": 2, "parameters": {"__IMTCONN__": 2160110}, "mapper": {"from": "share", "limit": "20", "filter": [[{"a": "N", "o": "notexist"}]], "sheetId": "Sheet1", "sortOrder": "asc", "spreadsheetId": "1dotBroTMKjiSErdtnwJUACwsvQwplnLWY4r_TV_Tvv4", "tableFirstRow": "A1:CZ1", "includesHeaders": true, "valueRenderOption": "FORMATTED_VALUE", "dateTimeRenderOption": "FORMATTED_STRING"}, "metadata": {"designer": {"x": 0, "y": 0}, "restore": {"expect": {"from": {"label": "Select from all"}, "orderBy": {"mode": "chose"}, "sheetId": {"mode": "chose", "label": "Sheet1"}, "sortOrder": {"mode": "chose", "label": "Ascending"}, "tableFirstRow": {"label": "A-CZ"}, "includesHeaders": {"mode": "chose", "label": "Yes"}, "valueRenderOption": {"mode": "chose", "label": "Formatted value"}, "dateTimeRenderOption": {"mode": "chose", "label": "Formatted string"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "from", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["drive", "share"]}}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "spreadsheetId", "type": "text", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1", "A1:AZZ1", "A1:BZZ1", "A1:CZZ1", "A1:DZZ1", "A1:MZZ1", "A1:ZZZ1"]}}, {"name": "filter", "type": "filter", "label": "Filter", "options": "rpc://google-sheets/2/rpcGetFilterKeys?includesHeaders=true"}, {"name": "orderBy", "type": "select", "label": "Order by"}, {"name": "sortOrder", "type": "select", "label": "Sort order", "validate": {"enum": ["asc", "desc"]}}], "interface": [{"name": "__IMTLENGTH__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total number of bundles"}, {"name": "__IMTINDEX__", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Bundle order position"}, {"name": "__ROW_NUMBER__", "type": "number", "label": "Row number"}, {"name": "__SPREADSHEET_ID__", "type": "text", "label": "Spreadsheet ID"}, {"name": "__SHEET__", "type": "text", "label": "Sheet"}, {"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "transcription (L)"}, {"name": "12", "type": "text", "label": "summary (M)"}, {"name": "13", "type": "text", "label": "Agent (N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}, {"name": "29", "type": "text", "label": "(AD)"}, {"name": "30", "type": "text", "label": "(AE)"}, {"name": "31", "type": "text", "label": "(AF)"}, {"name": "32", "type": "text", "label": "(AG)"}, {"name": "33", "type": "text", "label": "(AH)"}, {"name": "34", "type": "text", "label": "(AI)"}, {"name": "35", "type": "text", "label": "(AJ)"}, {"name": "36", "type": "text", "label": "(AK)"}, {"name": "37", "type": "text", "label": "(AL)"}, {"name": "38", "type": "text", "label": "(AM)"}, {"name": "39", "type": "text", "label": "(AN)"}, {"name": "40", "type": "text", "label": "(AO)"}, {"name": "41", "type": "text", "label": "(AP)"}, {"name": "42", "type": "text", "label": "(AQ)"}, {"name": "43", "type": "text", "label": "(AR)"}, {"name": "44", "type": "text", "label": "(AS)"}, {"name": "45", "type": "text", "label": "(AT)"}, {"name": "46", "type": "text", "label": "(AU)"}, {"name": "47", "type": "text", "label": "(AV)"}, {"name": "48", "type": "text", "label": "(AW)"}, {"name": "49", "type": "text", "label": "(AX)"}, {"name": "50", "type": "text", "label": "(AY)"}, {"name": "51", "type": "text", "label": "(AZ)"}, {"name": "52", "type": "text", "label": "(BA)"}, {"name": "53", "type": "text", "label": "(BB)"}, {"name": "54", "type": "text", "label": "(BC)"}, {"name": "55", "type": "text", "label": "(BD)"}, {"name": "56", "type": "text", "label": "(BE)"}, {"name": "57", "type": "text", "label": "(BF)"}, {"name": "58", "type": "text", "label": "(BG)"}, {"name": "59", "type": "text", "label": "(BH)"}, {"name": "60", "type": "text", "label": "(BI)"}, {"name": "61", "type": "text", "label": "(BJ)"}, {"name": "62", "type": "text", "label": "(BK)"}, {"name": "63", "type": "text", "label": "(BL)"}, {"name": "64", "type": "text", "label": "(BM)"}, {"name": "65", "type": "text", "label": "(BN)"}, {"name": "66", "type": "text", "label": "(BO)"}, {"name": "67", "type": "text", "label": "(BP)"}, {"name": "68", "type": "text", "label": "(BQ)"}, {"name": "69", "type": "text", "label": "(BR)"}, {"name": "70", "type": "text", "label": "(BS)"}, {"name": "71", "type": "text", "label": "(BT)"}, {"name": "72", "type": "text", "label": "(BU)"}, {"name": "73", "type": "text", "label": "(BV)"}, {"name": "74", "type": "text", "label": "(BW)"}, {"name": "75", "type": "text", "label": "(BX)"}, {"name": "76", "type": "text", "label": "(BY)"}, {"name": "77", "type": "text", "label": "(BZ)"}, {"name": "78", "type": "text", "label": "(CA)"}, {"name": "79", "type": "text", "label": "(CB)"}, {"name": "80", "type": "text", "label": "(CC)"}, {"name": "81", "type": "text", "label": "(CD)"}, {"name": "82", "type": "text", "label": "(CE)"}, {"name": "83", "type": "text", "label": "(CF)"}, {"name": "84", "type": "text", "label": "(CG)"}, {"name": "85", "type": "text", "label": "(CH)"}, {"name": "86", "type": "text", "label": "(CI)"}, {"name": "87", "type": "text", "label": "(CJ)"}, {"name": "88", "type": "text", "label": "(CK)"}, {"name": "89", "type": "text", "label": "(CL)"}, {"name": "90", "type": "text", "label": "(CM)"}, {"name": "91", "type": "text", "label": "(CN)"}, {"name": "92", "type": "text", "label": "(CO)"}, {"name": "93", "type": "text", "label": "(CP)"}, {"name": "94", "type": "text", "label": "(CQ)"}, {"name": "95", "type": "text", "label": "(CR)"}, {"name": "96", "type": "text", "label": "(CS)"}, {"name": "97", "type": "text", "label": "(CT)"}, {"name": "98", "type": "text", "label": "(CU)"}, {"name": "99", "type": "text", "label": "(CV)"}, {"name": "100", "type": "text", "label": "(CW)"}, {"name": "101", "type": "text", "label": "(CX)"}, {"name": "102", "type": "text", "label": "(CY)"}, {"name": "103", "type": "text", "label": "(CZ)"}]}}, {"id": 10, "module": "google-docs:getADocument", "version": 1, "parameters": {"__IMTCONN__": 2160110}, "mapper": {"filter": "image", "select": "map", "document": "{{5.`11`}}", "includeTabsContent": false}, "metadata": {"designer": {"x": 226, "y": 0}, "restore": {"expect": {"filter": {"label": "Image"}, "select": {"label": "By Mapping"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Get Content of a Document", "required": true, "validate": {"enum": ["map", "dropdown"]}}, {"name": "include<PERSON>abs<PERSON><PERSON>nt", "type": "boolean", "label": "Include Tabs Content", "required": true}, {"name": "filter", "type": "select", "label": "Filter", "required": true, "validate": {"enum": ["image", "drawing", "chart"]}}, {"name": "document", "type": "text", "label": "Document ID", "required": true}]}}, {"id": 7, "module": "openai-gpt-3:<PERSON>reateCompletion", "version": 1, "parameters": {"__IMTCONN__": 4089075}, "mapper": {"model": "chatgpt-4o-latest", "top_p": "1", "select": "chat", "messages": [{"role": "system", "content": "# Role\nAct as DemoAgent, a specialized AI that analyzes sales demo call transcripts to extract structured, actionable insights focused solely on the prospect’s contributions.\n\n# Task\nProcess a JSON-formatted transcript of a sales demo call. Your goal is to extract and structure key insights from the prospect’s feedback, questions, and feature requests while summarizing their overall perspective—without repeating standard internal demo segments.\n\n# Specific Instructions\n- **Speaker Identification:**  \n  - Identify and list internal speakers (typically <PERSON> and <PERSON>) versus external speakers (prospects or customers).  \n  - For external speakers, provide a brief note of their role (e.g., “Prospect: primary decision-maker”) if available.\n\n- **Extracted Insights:**  \n  - **Prospect Feedback:** Capture direct or indirect feedback, including any changes in sentiment or buyer intent (e.g., initial skepticism turning into interest).  \n  - **Key Questions and Answers:** Extract any questions asked by the prospect and, if available, the corresponding responses.  \n  - **Feature Requests:** Note any feature requests or suggestions mentioned by the prospect.\n  \n- **Summary:**  \n  - Create a concise summary focusing solely on the prospect’s perspective.  \n  - Include key details such as who the lead is, their existing setup, motivations, concerns, and overall intent.\n  \n- **Output Guidelines:**  \n  - Do not include a breakdown of standard demo phases (e.g., introductions, product demonstrations, pricing discussions) unless they directly pertain to the prospect’s contributions.  \n  - Avoid any extraneous or repetitive details regarding what internal speakers (<PERSON> or <PERSON>) said beyond what is needed to understand the prospect’s feedback.\n  - Do not output any prefatory text like “Below is the structured analysis…”—simply output the structured results.\n\n# Output Format\nReturn a structured markdown object that includes:\n- **speakers:** A list of identified speakers with their roles.\n- **extracted_insights:** An object containing:\n  - `prospect_feedback`: A summary of feedback and sentiment.\n  - `questions_and_answers`: A list of key Q&A pairs (if available).\n  - `feature_requests`: A list of any feature requests or suggestions.\n- **summary:** A concise summary of the prospect’s overall perspective, highlighting motivations, concerns, and intent."}, {"role": "user", "content": "{{10.text}}", "imageDetail": "auto"}], "max_tokens": "10000", "temperature": "1", "n_completions": "1", "response_format": "text"}, "metadata": {"designer": {"x": 521, "y": 7}, "restore": {"expect": {"stop": {"mode": "chose"}, "model": {"mode": "chose", "label": "chatgpt-4o-latest (system)"}, "select": {"label": "Create a Chat Completion (GPT and o1 models)"}, "messages": {"mode": "chose", "items": [{"role": {"mode": "chose", "label": "Developer / System"}}, {"role": {"mode": "chose", "label": "User"}, "imageDetail": {"mode": "chose", "label": "Auto"}, "imageInputType": {"mode": "chose", "label": "Empty"}}]}, "logit_bias": {"mode": "chose"}, "response_format": {"mode": "chose", "label": "Text"}, "additionalParameters": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "openai-gpt-3"}, "label": "<EMAIL>"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:openai-gpt-3", "label": "Connection", "required": true}], "expect": [{"name": "select", "type": "select", "label": "Select Method", "required": true, "validate": {"enum": ["chat", "prompt"]}}, {"name": "temperature", "type": "number", "label": "Temperature", "validate": {"max": 2, "min": 0}}, {"name": "top_p", "type": "number", "label": "Top P", "validate": {"max": 1, "min": 0}}, {"name": "n_completions", "type": "number", "label": "Number"}, {"name": "frequency_penalty", "type": "number", "label": "Frequency Penalty", "validate": {"max": 2, "min": -2}}, {"name": "presence_penalty", "type": "number", "label": "Presence Penalty", "validate": {"max": 2, "min": -2}}, {"name": "logit_bias", "spec": {"name": "value", "spec": [{"name": "token", "type": "text", "label": "Token ID", "required": true}, {"name": "probability", "type": "number", "label": "Probability", "required": true, "validate": {"max": 100, "min": -100}}], "type": "collection", "label": "Token Probability"}, "type": "array", "label": "Token Probability"}, {"name": "seed", "type": "integer", "label": "Seed"}, {"name": "stop", "spec": {"name": "value", "type": "text", "label": "Stop Sequence"}, "type": "array", "label": "Stop Sequences", "validate": {"maxItems": 4}}, {"name": "additionalParameters", "spec": {"name": "value", "spec": [{"name": "key", "type": "text", "label": "Parameter Name", "required": true}, {"name": "type", "type": "select", "label": "Input Type", "options": [{"label": "Text", "value": "text", "nested": [{"name": "value", "type": "text", "label": "Parameter Value"}], "default": true}, {"label": "Number", "value": "number", "nested": [{"name": "value", "type": "number", "label": "Parameter Value"}]}, {"label": "Boolean", "value": "boolean", "nested": [{"name": "value", "type": "boolean", "label": "Parameter Value"}]}, {"label": "Date", "value": "date", "nested": [{"name": "value", "type": "date", "label": "Parameter Value"}]}, {"label": "Any", "value": "any", "nested": [{"name": "value", "type": "any", "label": "Parameter Value"}]}]}], "type": "collection", "label": "Input Parameter"}, "type": "array", "label": "Other Input Parameters"}, {"name": "model", "type": "select", "label": "Model", "required": true}, {"name": "max_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Max Completion Tokens"}, {"name": "messages", "spec": {"name": "value", "spec": [{"name": "role", "type": "select", "label": "Role", "options": {"store": [{"label": "User", "value": "user", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}, {"name": "imageInputType", "type": "select", "label": "Image Input Type", "options": [{"label": "URL", "value": "url", "nested": [{"help": "Make sure to use a publicly accessible URL.\nYou can test if your image is publicly accessible by opening the link in an incognito tab.", "name": "imageUrl", "type": "url", "label": "Image URL"}]}, {"label": "Image File", "value": "file", "nested": [{"name": "imageFile", "spec": [{"help": "Accepted extensions: `.jpg`, `.jpeg`, `.png`, `.webp` and `.gif`.", "name": "imageFilename", "type": "filename", "label": "Image Filename", "semantic": "file:name", "extension": ["jpg", "jpeg", "png", "webp", "gif"]}, {"name": "imageData", "type": "buffer", "label": "Image Data", "semantic": "file:data"}], "type": "collection", "label": "Image"}]}], "mappable": false}, {"name": "imageDetail", "type": "select", "label": "Image Detail", "options": [{"label": "Auto", "value": "auto", "default": true}, {"label": "High", "value": "high"}, {"label": "Low", "value": "low"}], "advanced": true}]}, {"label": "Assistant", "value": "assistant", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}, {"label": "Developer / System", "value": "system", "nested": [{"help": "Text content of the message on behalf of the selected __Role__.", "name": "content", "type": "text", "label": "Text Content"}]}]}, "required": true}], "type": "collection", "label": "Message"}, "type": "array", "label": "Messages", "required": true}, {"name": "response_format", "type": "select", "label": "Response Format", "validate": {"enum": ["text", "json_object"]}}, {"name": "prediction", "type": "text", "label": "Predicted Outputs"}], "interface": [{"name": "result", "type": "any", "label": "Result"}, {"name": "id", "type": "text", "label": "ID"}, {"name": "object", "type": "text", "label": "Object"}, {"name": "created", "type": "date", "label": "Created"}, {"name": "model", "type": "text", "label": "Model"}, {"name": "choices", "spec": {"spec": [{"name": "text", "type": "text", "label": "Text"}, {"name": "index", "type": "number", "label": "Index"}, {"name": "logprobs", "type": "text", "label": "Log Probs"}, {"name": "finish_reason", "type": "text", "label": "Finish Reason"}, {"name": "message", "spec": [{"name": "role", "type": "text", "label": "Role"}, {"name": "content", "type": "text", "label": "Content"}, {"name": "refusal", "type": "text", "label": "Refusal"}], "type": "collection", "label": "Message"}], "type": "collection"}, "type": "array", "label": "Choices"}, {"name": "usage", "spec": [{"name": "prompt_tokens", "type": "number", "label": "Prompt Tokens"}, {"name": "completion_tokens", "type": "text", "label": "Completion Tokens"}, {"name": "total_tokens", "type": "number", "label": "Total Tokens"}, {"name": "prompt_tokens_details", "spec": [{"name": "cached_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "image_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Image Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}], "type": "collection", "label": "Prompt Tokens Details"}, {"name": "completion_tokens_details", "spec": [{"name": "reasoning_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Reasoning Tokens"}, {"name": "text_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Text Tokens"}, {"name": "audio_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Audio Tokens"}, {"name": "accepted_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Accepted Prediction Tokens"}, {"name": "rejected_prediction_tokens", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rejected Prediction Tokens"}], "type": "collection", "label": "Completion Tokens Details"}], "type": "collection", "label": "Usage"}, {"name": "service_tier", "type": "text", "label": "Service Tier"}, {"name": "system_fingerprint", "type": "text", "label": "System Fingerprint"}], "advanced": true}}, {"id": 9, "module": "google-sheets:updateRow", "version": 2, "parameters": {"__IMTCONN__": 2160110}, "mapper": {"mode": "fromAll", "values": {"13": "{{7.result}}"}, "sheetId": "Sheet1", "rowNumber": "{{5.`__ROW_NUMBER__`}}", "spreadsheetId": "1dotBroTMKjiSErdtnwJUACwsvQwplnLWY4r_TV_Tvv4", "includesHeaders": true, "valueInputOption": "USER_ENTERED"}, "metadata": {"designer": {"x": 835, "y": 11}, "restore": {"expect": {"mode": {"label": "Select from all"}, "sheetId": {"mode": "chose", "label": "Sheet1"}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "transcription (L)"}, {"name": "12", "type": "text", "label": "summary (M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}, "valueInputOption": {"mode": "chose", "label": "User entered"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "spreadsheetId", "type": "text", "label": "Spreadsheet ID", "required": true}, {"mode": "edit", "name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "rowNumber", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Row number", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "transcription (L)"}, {"name": "12", "type": "text", "label": "summary (M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "eu2.make.com", "notes": []}}