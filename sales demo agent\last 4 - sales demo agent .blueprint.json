{"name": "last 4 - sales demo agent ", "flow": [{"id": 1, "module": "google-sheets:filterRows", "version": 2, "parameters": {"__IMTCONN__": 2160110}, "mapper": {"from": "share", "filter": [[{"a": "O", "o": "notexist"}]], "sheetId": "Sheet1", "sortOrder": "asc", "spreadsheetId": "1dotBroTMKjiSErdtnwJUACwsvQwplnLWY4r_TV_Tvv4", "tableFirstRow": "A1:CZ1", "includesHeaders": true, "valueRenderOption": "FORMATTED_VALUE", "dateTimeRenderOption": "FORMATTED_STRING"}, "metadata": {"designer": {"x": 0, "y": 0}, "restore": {"expect": {"from": {"label": "Select from all"}, "orderBy": {"mode": "chose"}, "sheetId": {"mode": "chose", "label": "Sheet1"}, "sortOrder": {"mode": "chose", "label": "Ascending"}, "tableFirstRow": {"label": "A-CZ"}, "includesHeaders": {"mode": "chose", "label": "Yes"}, "valueRenderOption": {"mode": "chose", "label": "Formatted value"}, "dateTimeRenderOption": {"mode": "chose", "label": "Formatted string"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "from", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["drive", "share"]}}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "spreadsheetId", "type": "text", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1", "A1:AZZ1", "A1:BZZ1", "A1:CZZ1", "A1:DZZ1", "A1:MZZ1", "A1:ZZZ1"]}}, {"name": "filter", "type": "filter", "label": "Filter", "options": "rpc://google-sheets/2/rpcGetFilterKeys?includesHeaders=true"}, {"name": "orderBy", "type": "select", "label": "Order by"}, {"name": "sortOrder", "type": "select", "label": "Sort order", "validate": {"enum": ["asc", "desc"]}}], "interface": [{"name": "__IMTLENGTH__", "label": "Total number of bundles", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "__IMTINDEX__", "label": "Bundle order position", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "__ROW_NUMBER__", "type": "number", "label": "Row number"}, {"name": "__SPREADSHEET_ID__", "type": "text", "label": "Spreadsheet ID"}, {"name": "__SHEET__", "type": "text", "label": "Sheet"}, {"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "transcription (L)"}, {"name": "12", "type": "text", "label": "summary (M)"}, {"name": "13", "type": "text", "label": "agent (N)"}, {"name": "14", "type": "text", "label": "sent (O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}, {"name": "29", "type": "text", "label": "(AD)"}, {"name": "30", "type": "text", "label": "(AE)"}, {"name": "31", "type": "text", "label": "(AF)"}, {"name": "32", "type": "text", "label": "(AG)"}, {"name": "33", "type": "text", "label": "(AH)"}, {"name": "34", "type": "text", "label": "(AI)"}, {"name": "35", "type": "text", "label": "(AJ)"}, {"name": "36", "type": "text", "label": "(AK)"}, {"name": "37", "type": "text", "label": "(AL)"}, {"name": "38", "type": "text", "label": "(AM)"}, {"name": "39", "type": "text", "label": "(AN)"}, {"name": "40", "type": "text", "label": "(AO)"}, {"name": "41", "type": "text", "label": "(AP)"}, {"name": "42", "type": "text", "label": "(AQ)"}, {"name": "43", "type": "text", "label": "(AR)"}, {"name": "44", "type": "text", "label": "(AS)"}, {"name": "45", "type": "text", "label": "(AT)"}, {"name": "46", "type": "text", "label": "(AU)"}, {"name": "47", "type": "text", "label": "(AV)"}, {"name": "48", "type": "text", "label": "(AW)"}, {"name": "49", "type": "text", "label": "(AX)"}, {"name": "50", "type": "text", "label": "(AY)"}, {"name": "51", "type": "text", "label": "(AZ)"}, {"name": "52", "type": "text", "label": "(BA)"}, {"name": "53", "type": "text", "label": "(BB)"}, {"name": "54", "type": "text", "label": "(BC)"}, {"name": "55", "type": "text", "label": "(BD)"}, {"name": "56", "type": "text", "label": "(BE)"}, {"name": "57", "type": "text", "label": "(BF)"}, {"name": "58", "type": "text", "label": "(BG)"}, {"name": "59", "type": "text", "label": "(BH)"}, {"name": "60", "type": "text", "label": "(BI)"}, {"name": "61", "type": "text", "label": "(BJ)"}, {"name": "62", "type": "text", "label": "(BK)"}, {"name": "63", "type": "text", "label": "(BL)"}, {"name": "64", "type": "text", "label": "(BM)"}, {"name": "65", "type": "text", "label": "(BN)"}, {"name": "66", "type": "text", "label": "(BO)"}, {"name": "67", "type": "text", "label": "(BP)"}, {"name": "68", "type": "text", "label": "(BQ)"}, {"name": "69", "type": "text", "label": "(BR)"}, {"name": "70", "type": "text", "label": "(BS)"}, {"name": "71", "type": "text", "label": "(BT)"}, {"name": "72", "type": "text", "label": "(BU)"}, {"name": "73", "type": "text", "label": "(BV)"}, {"name": "74", "type": "text", "label": "(BW)"}, {"name": "75", "type": "text", "label": "(BX)"}, {"name": "76", "type": "text", "label": "(BY)"}, {"name": "77", "type": "text", "label": "(BZ)"}, {"name": "78", "type": "text", "label": "(CA)"}, {"name": "79", "type": "text", "label": "(CB)"}, {"name": "80", "type": "text", "label": "(CC)"}, {"name": "81", "type": "text", "label": "(CD)"}, {"name": "82", "type": "text", "label": "(CE)"}, {"name": "83", "type": "text", "label": "(CF)"}, {"name": "84", "type": "text", "label": "(CG)"}, {"name": "85", "type": "text", "label": "(CH)"}, {"name": "86", "type": "text", "label": "(CI)"}, {"name": "87", "type": "text", "label": "(CJ)"}, {"name": "88", "type": "text", "label": "(CK)"}, {"name": "89", "type": "text", "label": "(CL)"}, {"name": "90", "type": "text", "label": "(CM)"}, {"name": "91", "type": "text", "label": "(CN)"}, {"name": "92", "type": "text", "label": "(CO)"}, {"name": "93", "type": "text", "label": "(CP)"}, {"name": "94", "type": "text", "label": "(CQ)"}, {"name": "95", "type": "text", "label": "(CR)"}, {"name": "96", "type": "text", "label": "(CS)"}, {"name": "97", "type": "text", "label": "(CT)"}, {"name": "98", "type": "text", "label": "(CU)"}, {"name": "99", "type": "text", "label": "(CV)"}, {"name": "100", "type": "text", "label": "(CW)"}, {"name": "101", "type": "text", "label": "(CX)"}, {"name": "102", "type": "text", "label": "(CY)"}, {"name": "103", "type": "text", "label": "(CZ)"}]}}, {"id": 4, "module": "regexp:<PERSON>lace", "version": 1, "parameters": {}, "mapper": {"text": "{{1.`13`}}", "value": "{{emptystring}}", "global": true, "pattern": "(```markdown\\n)|(\\n```)", "multiline": true, "sensitive": false, "singleline": false}, "metadata": {"designer": {"x": 234, "y": -27}, "restore": {"expect": {"global": {"mode": "chose"}, "multiline": {"mode": "chose"}, "sensitive": {"mode": "chose"}, "singleline": {"mode": "chose"}}}, "expect": [{"name": "pattern", "type": "text", "label": "Pattern", "required": true}, {"name": "value", "type": "text", "label": "New value"}, {"name": "global", "type": "boolean", "label": "Global match", "required": true}, {"name": "sensitive", "type": "boolean", "label": "Case sensitive", "required": true}, {"name": "multiline", "type": "boolean", "label": "Multiline", "required": true}, {"name": "singleline", "type": "boolean", "label": "Singleline", "required": true}, {"name": "text", "type": "text", "label": "Text"}]}}, {"id": 5, "module": "regexp:<PERSON>lace", "version": 1, "parameters": {}, "mapper": {"text": "{{4.text}}", "value": "*", "global": true, "pattern": "\\*\\*", "multiline": true, "sensitive": false, "singleline": false}, "metadata": {"designer": {"x": 505, "y": -19}, "restore": {"expect": {"global": {"mode": "chose"}, "multiline": {"mode": "chose"}, "sensitive": {"mode": "chose"}, "singleline": {"mode": "chose"}}}, "expect": [{"name": "pattern", "type": "text", "label": "Pattern", "required": true}, {"name": "value", "type": "text", "label": "New value"}, {"name": "global", "type": "boolean", "label": "Global match", "required": true}, {"name": "sensitive", "type": "boolean", "label": "Case sensitive", "required": true}, {"name": "multiline", "type": "boolean", "label": "Multiline", "required": true}, {"name": "singleline", "type": "boolean", "label": "Singleline", "required": true}, {"name": "text", "type": "text", "label": "Text"}]}}, {"id": 2, "module": "slack:Create<PERSON><PERSON><PERSON>", "version": 4, "parameters": {"__IMTCONN__": 6426733}, "mapper": {"text": "🆕 *{{1.`0`}}*\nDate: {{1.`2`}}\n{{5.text}}", "parse": true, "mrkdwn": true, "channel": "C08HCMXSW30", "channelType": "public", "channelWType": "list"}, "metadata": {"designer": {"x": 718, "y": 6}, "restore": {"expect": {"parse": {"mode": "chose"}, "mrkdwn": {"mode": "chose"}, "channel": {"mode": "chose", "label": "sales-demo-agent"}, "link_names": {"mode": "chose"}, "channelType": {"label": "Public channel"}, "channelWType": {"label": "Select from the list"}, "unfurl_links": {"mode": "chose"}, "unfurl_media": {"mode": "chose"}, "reply_broadcast": {"mode": "chose"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "slack3"}, "label": "JatheonDemoAgent-Real (jatheondemoagent)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:slack2,slack3", "label": "Connection", "required": true}], "expect": [{"name": "channelWType", "type": "select", "label": "Enter a channel ID or name", "required": true, "validate": {"enum": ["manualy", "list"]}}, {"name": "text", "type": "text", "label": "Text"}, {"name": "blocks", "type": "text", "label": "Blocks"}, {"name": "thread_ts", "type": "text", "label": "Thread message ID (time stamp)"}, {"name": "reply_broadcast", "type": "boolean", "label": "Reply broadcast"}, {"name": "link_names", "type": "boolean", "label": "Link names"}, {"name": "parse", "type": "boolean", "label": "Parse message text"}, {"name": "mrkdwn", "type": "boolean", "label": "Use markdown"}, {"name": "unfurl_links", "type": "boolean", "label": "Unfurl primarily text-based content"}, {"name": "unfurl_media", "type": "boolean", "label": "Unfurl media content"}, {"name": "icon_emoji", "type": "text", "label": "Icon emoji"}, {"name": "icon_url", "type": "url", "label": "Icon url"}, {"name": "username", "type": "text", "label": "User name"}, {"name": "channelType", "type": "select", "label": "Channel type", "required": true, "validate": {"enum": ["public", "private", "im", "mpim"]}}, {"name": "channel", "type": "select", "label": "Public channel", "required": true}], "advanced": true}}, {"id": 6, "module": "google-sheets:updateRow", "version": 2, "parameters": {"__IMTCONN__": 2160110}, "mapper": {"mode": "fromAll", "values": {"14": "True"}, "sheetId": "Sheet1", "rowNumber": "{{1.`__ROW_NUMBER__`}}", "spreadsheetId": "1dotBroTMKjiSErdtnwJUACwsvQwplnLWY4r_TV_Tvv4", "includesHeaders": true, "valueInputOption": "USER_ENTERED"}, "metadata": {"designer": {"x": 1018, "y": 6}, "restore": {"expect": {"mode": {"label": "Select from all"}, "sheetId": {"mode": "chose", "label": "Sheet1"}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "transcription (L)"}, {"name": "12", "type": "text", "label": "summary (M)"}, {"name": "13", "type": "text", "label": "agent (N)"}, {"name": "14", "type": "text", "label": "sent (O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}, "valueInputOption": {"mode": "chose", "label": "User entered"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "spreadsheetId", "type": "text", "label": "Spreadsheet ID", "required": true}, {"mode": "edit", "name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "rowNumber", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Row number", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "transcription (L)"}, {"name": "12", "type": "text", "label": "summary (M)"}, {"name": "13", "type": "text", "label": "agent (N)"}, {"name": "14", "type": "text", "label": "sent (O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "eu2.make.com", "notes": []}}