#!/usr/bin/env python3
import requests
import base64
import json

# CREDENTIALS
CLIENT_ID = "gSUHotjR5G89Lo7Y91l9Q"
CLIENT_SECRET = "eJ7xowTFogFILrm9i8ocGxZSL4W3c97w"
ACCOUNT_ID = "FGcAlREpQgGfY8G9wM6qsw"

# WHO CREATED THE APP - THIS IS THE ISSUE
APP_CREATOR = "<EMAIL>"  # BASIC LICENSE
# USERS WITH PRO LICENSE WHO HAVE RECORDINGS
PRO_USERS = ["<EMAIL>", "<EMAIL>"]

def get_token():
    """Get access token"""
    auth_string = f"{CLIENT_ID}:{CLIENT_SECRET}"
    auth_b64 = base64.b64encode(auth_string.encode()).decode()
    
    # Try with explicit scope request
    response = requests.post(
        'https://zoom.us/oauth/token',
        headers={'Authorization': f'Basic {auth_b64}', 'Content-Type': 'application/x-www-form-urlencoded'},
        data={
            'grant_type': 'account_credentials',
            'account_id': ACCOUNT_ID,
            'scope': 'cloud_recording:read:list_account_recordings:admin cloud_recording:read:recording:admin user:read:user:admin user:read:list_users:admin'
        }
    )
    
    return response.json()['access_token']

def decode_token_scopes(token):
    """Decode JWT token to see what scopes are actually in it"""
    print("🔓 DECODING TOKEN TO SEE ACTUAL SCOPES")
    print("=" * 70)
    
    try:
        # Split JWT token into parts
        parts = token.split('.')
        if len(parts) >= 2:
            # Decode the payload (second part)
            payload = parts[1]
            # Add padding if needed
            padding = len(payload) % 4
            if padding:
                payload += '=' * (4 - padding)
            
            decoded_bytes = base64.b64decode(payload)
            decoded = json.loads(decoded_bytes)
            
            print("✅ Token decoded successfully!")
            
            # Look for scopes in different possible fields
            scopes = decoded.get('scp', decoded.get('scope', decoded.get('scopes', [])))
            
            if scopes:
                if isinstance(scopes, str):
                    scopes = scopes.split(' ')
                
                print(f"🔑 SCOPES IN TOKEN ({len(scopes)}):")
                for scope in sorted(scopes):
                    print(f"   • {scope}")
                
                # Check for the specific scopes you have enabled
                expected_scopes = [
                    'cloud_recording:read:list_account_recordings:master',
                    'cloud_recording:read:list_user_recordings:master', 
                    'cloud_recording:read:recording:master',
                    'cloud_recording:read:archive_files:admin',
                    'account:read:account_setting:master',
                    'account:read:settings:master',
                    'meeting:read:meeting:admin',
                    'cloud_recording:read:list_account_recordings:admin',
                    'cloud_recording:read:recording:admin',
                    'cloud_recording:read:list_user_recordings:admin',
                    'user:read:user:admin',
                    'user:read:list_users:admin'
                ]
                
                print(f"\n🎯 SCOPE CHECK:")
                missing_critical = []
                for expected in expected_scopes:
                    if expected in scopes:
                        print(f"   ✅ {expected}")
                    else:
                        print(f"   ❌ {expected} - MISSING!")
                        missing_critical.append(expected)
                
                if missing_critical:
                    print(f"\n🚨 CRITICAL ISSUE: {len(missing_critical)} scopes are enabled in app but missing from token!")
                    print("   This means your app configuration is not taking effect.")
                    print("   Solution: Deactivate and reactivate your app, then wait 10 minutes.")
                
            else:
                print("⚠️  No scopes found in token")
                print("📝 Full token contents:")
                for key, value in decoded.items():
                    print(f"   {key}: {value}")
    
    except Exception as e:
        print(f"❌ Could not decode token: {e}")
        print("🔍 Token might be encrypted or in different format")
    
    print()

def show_token_scopes(token):
    """Show exactly what scopes are in the token"""
    print("🔑 CHECKING TOKEN SCOPES")
    print("=" * 70)
    
    # First decode the token to see what's actually in it
    decode_token_scopes(token)
    
    # Then test basic endpoint
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get('https://api.zoom.us/v2/users/me', headers=headers)
    
    if response.status_code == 200:
        print("✅ Token is valid")
        user_data = response.json()
        print(f"   👤 User: {user_data.get('email', 'Unknown')}")
        print(f"   🏢 Account: {user_data.get('account_id', 'Unknown')}")
    else:
        print(f"❌ Token test failed: {response.status_code}")
        print(f"   Error: {response.text}")
    
    print()

def test_critical_endpoints(token):
    """Test the specific endpoints that should work with your enabled scopes"""
    print("🧪 TESTING CRITICAL ENDPOINTS THAT SHOULD WORK")
    print("=" * 70)
    
    headers = {'Authorization': f'Bearer {token}'}
    
    critical_tests = [
        {
            'name': 'Account Recordings (Admin)',
            'url': f'https://api.zoom.us/v2/accounts/me/recordings?from=2025-03-13&to=2025-07-23&page_size=300&trash_type=meeting_recordings&trash=false',
            'expected_scope': 'cloud_recording:read:list_account_recordings:admin',
            'description': 'This should give you ALL account recordings including archived'
        },
        {
            'name': 'All Users Recordings',
            'url': f'https://api.zoom.us/v2/accounts/me/recordings?from=2025-03-13&to=2025-07-23&page_size=300&trash_type=meeting_recordings',
            'expected_scope': 'cloud_recording:read:list_account_recordings:admin',
            'description': 'This includes trashed and archived recordings'
        }
    ]
    
    for test in critical_tests:
        print(f"🔍 {test['name']}")
        print(f"   📝 {test['description']}")
        print(f"   🔑 Needs: {test['expected_scope']}")
        print(f"   🌐 URL: {test['url']}")
        
        try:
            response = requests.get(test['url'], headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                meetings = data.get('meetings', [])
                archive_files = data.get('archive_files', [])
                total_records = data.get('total_records', 0)
                
                count = len(meetings) + len(archive_files)
                
                print(f"   ✅ SUCCESS! Found {count} items (Total: {total_records})")
                
                # Check pagination
                if 'next_page_token' in data:
                    print(f"   📄 Has more pages! Use pagination to get all.")
                    
                if count >= 90:
                    print(f"   🏆 JACKPOT! This has {count} items - this is your answer!")
                    print(f"   📋 USE THIS URL IN MAKE.COM: {test['url']}")
                    return test['url']
                elif total_records > count:
                    print(f"   ⚠️  API says {total_records} total but only returned {count} - PAGINATION NEEDED!")
                    print(f"   📋 Add pagination with next_page_token to get all {total_records} recordings")
                    
            else:
                data = response.json() if response.text else {}
                error = data.get('message', 'Unknown error')
                print(f"   ❌ FAILED: {response.status_code}")
                print(f"   📝 Error: {error}")
                
        except Exception as e:
            print(f"   💥 Exception: {e}")
        
        print()
    
    return None

def test_all_endpoints(token):
    """Test every possible endpoint to find the missing 79 recordings"""
    
    headers = {'Authorization': f'Bearer {token}'}
    
    # All possible endpoints to try
    endpoints = [
        # Regular recordings (we know this gives 20)
        {
            'name': 'Account Recordings (me)',
            'url': "https://api.zoom.us/v2/accounts/me/recordings?from=2025-03-13&to=2025-07-23&page_size=300"
        },
        
        # Account-specific recordings
        {
            'name': 'Account Recordings (specific ID)',
            'url': f"https://api.zoom.us/v2/accounts/{ACCOUNT_ID}/recordings?from=2025-03-13&to=2025-07-23&page_size=300"
        },
        
        # Archive endpoints (these might have the missing 79)
        {
            'name': 'Archive Files (me)',
            'url': "https://api.zoom.us/v2/accounts/me/archive_files?from=2025-03-13&to=2025-07-23&page_size=300"
        },
        {
            'name': 'Archive Files (specific ID)',
            'url': f"https://api.zoom.us/v2/accounts/{ACCOUNT_ID}/archive_files?from=2025-03-13&to=2025-07-23&page_size=300"
        },
        {
            'name': 'Archive Files (global)',
            'url': "https://api.zoom.us/v2/archive_files?from=2025-03-13&to=2025-07-23&page_size=300"
        },
        
        # User-specific recordings
        {
            'name': 'User Recordings (me)',
            'url': "https://api.zoom.us/v2/users/me/recordings?from=2025-03-13&to=2025-07-23&page_size=300"
        },
        
        # Try without date range (get everything recent)
        {
            'name': 'All Recent Recordings (me)',
            'url': "https://api.zoom.us/v2/accounts/me/recordings?page_size=300"
        },
        {
            'name': 'All Recent Recordings (specific ID)',
            'url': f"https://api.zoom.us/v2/accounts/{ACCOUNT_ID}/recordings?page_size=300"
        },
        
        # Try meetings endpoint (might have recording info)
        {
            'name': 'Previous Meetings',
            'url': "https://api.zoom.us/v2/users/me/meetings?type=previous_meetings&page_size=300"
        },
        
        # Try with different date formats
        {
            'name': 'Account Recordings (ISO dates)',
            'url': "https://api.zoom.us/v2/accounts/me/recordings?from=2025-03-13T00:00:00Z&to=2025-07-23T23:59:59Z&page_size=300"
        },
        
        # Try cloud recording endpoints
        {
            'name': 'Cloud Recordings',
            'url': "https://api.zoom.us/v2/accounts/me/cloud_recording?from=2025-03-13&to=2025-07-23&page_size=300"
        },
        
        # Try meetings with recordings
        {
            'name': 'Meetings (all)',
            'url': "https://api.zoom.us/v2/users/me/meetings?type=all&page_size=300"
        },
        
        # Try with trash parameter
        {
            'name': 'Account Recordings (including trash)',
            'url': "https://api.zoom.us/v2/accounts/me/recordings?from=2025-03-13&to=2025-07-23&page_size=300&trash=true"
        },
        
        # Try each user's recordings separately
        {
            'name': 'JMarlow User Recordings',
            'url': "https://api.zoom.us/v2/users/<EMAIL>/recordings?from=2025-03-13&to=2025-07-23&page_size=300"
        },
        {
            'name': 'Vlad User Recordings',
            'url': "https://api.zoom.us/v2/users/<EMAIL>/recordings?from=2025-03-13&to=2025-07-23&page_size=300"
        }
    ]
    
    results = []
    
    print("🔍 TESTING ALL POSSIBLE ENDPOINTS FOR MISSING RECORDINGS")
    print("Expected: 99 recordings | Currently found: 20")
    print("=" * 70)
    
    for i, endpoint_info in enumerate(endpoints, 1):
        name = endpoint_info['name']
        url = endpoint_info['url']
        
        print(f"\n🔍 TEST {i}: {name}")
        print(f"   URL: {url}")
        
        try:
            response = requests.get(url, headers=headers)
            
            if response.text:
                try:
                    data = response.json()
                except:
                    data = {'error': 'Invalid JSON response'}
            else:
                data = {'error': 'Empty response'}
            
            if response.status_code == 200:
                # Count different types of results
                meetings = data.get('meetings', [])
                recordings = data.get('recordings', [])
                archive_files = data.get('archive_files', [])
                cloud_recordings = data.get('cloud_recordings', [])
                total_records = data.get('total_records', 0)
                
                count = len(meetings) + len(recordings) + len(archive_files) + len(cloud_recordings)
                
                print(f"   ✅ SUCCESS! Found {count} items")
                print(f"   📊 Total Records (API): {total_records}")
                
                if meetings:
                    print(f"   🎥 Meetings: {len(meetings)}")
                if recordings:
                    print(f"   📹 Recordings: {len(recordings)}")
                if archive_files:
                    print(f"   🗄️  Archive Files: {len(archive_files)}")
                if cloud_recordings:
                    print(f"   ☁️  Cloud Recordings: {len(cloud_recordings)}")
                
                if count > 20:
                    print(f"   🎉 FOUND MORE THAN 20! This might be it!")
                    
                if count >= 90:
                    print(f"   🏆 JACKPOT! This has {count} recordings - very close to 99!")
                    
                results.append({
                    'name': name,
                    'url': url,
                    'count': count,
                    'total_records': total_records,
                    'success': True,
                    'data': data
                })
                
            else:
                print(f"   ❌ FAILED: {response.status_code}")
                
                if isinstance(data, dict):
                    error_msg = data.get('message', data.get('error', 'Unknown error'))
                    print(f"   📝 Error: {error_msg}")
                    
                    if 'scope' in str(error_msg).lower():
                        # Extract the missing scope
                        if '[' in str(error_msg) and ']' in str(error_msg):
                            missing_scope = str(error_msg).split('[')[1].split(']')[0]
                            print(f"   🔑 Missing Scope: {missing_scope}")
                        else:
                            print(f"   🔑 Scope issue detected")
                else:
                    print(f"   📝 Raw response: {response.text[:200]}...")
                
                results.append({
                    'name': name,
                    'url': url,
                    'count': 0,
                    'total_records': 0,
                    'success': False,
                    'error': data
                })
                    
        except Exception as e:
            print(f"   💥 Exception: {e}")
            results.append({
                'name': name,
                'url': url,
                'count': 0,
                'total_records': 0,
                'success': False,
                'error': str(e)
            })
    
    return results

def analyze_results(results):
    """Analyze which endpoint has the most recordings"""
    print(f"\n🎯 ANALYSIS - WHICH ENDPOINT HAS THE MOST RECORDINGS?")
    print("=" * 70)
    
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    if not successful_results:
        print("❌ No successful endpoints found")
        print("\n🔍 All endpoints failed. Missing scopes:")
        for result in failed_results:
            if 'scope' in str(result.get('error', '')).lower():
                print(f"   • {result['name']}: {result['error']}")
        return
    
    # Sort by count
    successful_results.sort(key=lambda x: x['count'], reverse=True)
    
    print(f"📊 Found {len(successful_results)} working endpoints:")
    
    for i, result in enumerate(successful_results, 1):
        count = result['count']
        total = result['total_records']
        name = result['name']
        
        if count >= 90:
            status = "🏆 JACKPOT"
        elif count > 20:
            status = "🎉 PROMISING"
        else:
            status = "📝 STANDARD"
        
        print(f"{i}. {status}: {name}")
        print(f"   📊 Found: {count} recordings")
        print(f"   📈 API Total: {total}")
        print()
    
    # Show the best option
    best = successful_results[0]
    if best['count'] >= 90:
        print(f"🎯 USE THIS ENDPOINT: {best['name']}")
        print(f"   URL: {best['url']}")
        print(f"   This found {best['count']} recordings - closest to your expected 99!")
        print(f"\n📋 FOR MAKE.COM:")
        print(f"   1. Update HTTP Request URL to: {best['url']}")
        print(f"   2. Keep your Bearer token")
        print(f"   3. Run the automation")
    else:
        print(f"⚠️  No single endpoint found all 99 recordings.")
        print(f"   Best option: {best['name']} with {best['count']} recordings")
        
    # Show failed endpoints and missing scopes
    if failed_results:
        print(f"\n❌ FAILED ENDPOINTS AND MISSING SCOPES:")
        print("=" * 50)
        for result in failed_results:
            error = result.get('error', 'Unknown error')
            print(f"   • {result['name']}")
            if 'scope' in str(error).lower():
                print(f"     🔑 Scope issue: {error}")
            else:
                print(f"     ❌ Error: {error}")

def main():
    print("🚀 FIND THE MISSING 79 RECORDINGS - SCOPE DIAGNOSTIC")
    print("Expected: 99 recordings | Currently found: 20")
    print("=" * 70)
    
    token = get_token()
    show_token_scopes(token)
    
    # Test the critical endpoints first
    winning_url = test_critical_endpoints(token)
    
    if winning_url:
        print(f"🎯 SOLUTION FOUND!")
        print(f"URL: {winning_url}")
        print("\n📋 NEXT STEPS:")
        print("1. Copy the URL above")
        print("2. Update Make.com Automation 1 HTTP Request")
        print("3. Run the automation")
        return
    
    print(f"\n🚨 CRITICAL ENDPOINTS FAILED - SCOPE ISSUE CONFIRMED")
    print(f"Your enabled scopes are NOT making it into the token.")
    print(f"\n🔧 SOLUTION:")
    print(f"1. Go to https://developers.zoom.us/apps")
    print(f"2. Click 'Sales Demo Agent'")
    print(f"3. DEACTIVATE the app")
    print(f"4. Wait 2 minutes") 
    print(f"5. REACTIVATE the app")
    print(f"6. Wait 10 minutes")
    print(f"7. Run this script again")
    print(f"\nThis will force Zoom to regenerate tokens with your enabled scopes.")

if __name__ == "__main__":
    main() 