# ENHANCED WORKFLOW STRATEGY

## Problem: AI Ignores System Prompt Rules
The current system prompt approach is failing because:
- AI selectively follows instructions
- Complex retry logic is ignored
- Limit parameters don't work
- Cross-system name matching isn't happening

## Solution: Workflow-Level Logic

### 1. MANDATORY Error Handling Nodes
Add Try/Catch nodes around each tool group:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HubSpot       │    │   Zendesk       │    │    Xero         │
│   Search        │    │   Retry Logic   │    │   Search        │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │Try: Company │ │    │ │Try: Original│ │    │ │Try: Company │ │
│ │Name Search  │ │    │ │Name         │ │    │ │Name         │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│                 │    │ │Catch: Short │ │    │ │Catch: Short │ │
│                 │    │ │Name         │ │    │ │Name         │ │
│                 │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │ ┌─────────────┐ │    │                 │
│                 │    │ │Catch: Core  │ │    │                 │
│                 │    │ │Name         │ │    │                 │
│                 │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. FORCED Cross-System Name Matching
Use JavaScript nodes to extract company names:

```javascript
// Extract company name from HubSpot results
const hubspotResults = $('HubSpot_Companies').all();
let companyName = '';

if (hubspotResults.length > 0) {
  companyName = hubspotResults[0].json.properties.name;
  
  // Use this exact name for other searches
  return [{
    json: {
      original_query: $json.query,
      hubspot_company_name: companyName,
      search_variations: [
        companyName, // Full name
        companyName.replace(/ Inc\.?| LLC\.?| Corp\.?/gi, ''), // Remove suffixes
        companyName.split(' ')[0], // First word only
        companyName.replace(/[&\-]/g, ' ') // Remove special chars
      ]
    }
  }];
}
```

### 3. HARD Response Size Limits
Force limits at the tool level:

```javascript
// Before each search, calculate remaining context
const maxTokens = 120000; // Leave buffer
const currentTokens = $('Calculate_Tokens').first().json.token_count;
const remainingTokens = maxTokens - currentTokens;

// Calculate safe limit (rough estimate: 1 record = 200 tokens)
const safeLimit = Math.min(50, Math.floor(remainingTokens / 200));

return [{
  json: {
    limit: safeLimit,
    truncate_response: safeLimit < 10
  }
}];
```

### 4. SIMPLIFIED System Prompt
Reduce AI system prompt to bare essentials:

```markdown
# CUSTOMER AI AGENT - SIMPLIFIED

## CRITICAL RULES
1. Use company name provided in each tool call exactly as given
2. ALWAYS add limit parameter to every search
3. If no results, respond with "No results found"
4. Format response by system: [Zendesk], [HubSpot], [Xero]
5. NEVER rank RSD amounts (ignore them completely)

## RESPONSE SIZE PROTECTION
- If any tool returns >30 results, show only top 10
- If response approaches limits, summarize key findings
- Use "showing top X results" instead of full lists
```

### 5. MANDATORY Pre-Processing Node
Add a node that:
- Extracts company name from user query
- Generates search variations
- Calculates context limits
- Passes structured data to AI

### 6. FORCE Token Counting
Add a node that tracks token usage:

```javascript
// Rough token estimation
function estimateTokens(text) {
  return Math.ceil(text.length / 4); // Rough estimate
}

const currentResponse = $json.response;
const tokenCount = estimateTokens(JSON.stringify(currentResponse));

if (tokenCount > 100000) {
  // Truncate response
  return [{
    json: {
      response: "Response too large - showing summary only",
      full_response_available: false,
      token_count: tokenCount
    }
  }];
}
```

## Implementation Priority:

1. **IMMEDIATE**: Add error handling nodes with Try/Catch
2. **IMMEDIATE**: Force hard limits (limit=10) on all tools
3. **IMMEDIATE**: Add cross-system name matching logic
4. **MEDIUM**: Implement token counting and truncation
5. **MEDIUM**: Simplify system prompt to bare essentials

## Alternative: Fine-Tuning Approach

If workflow-level fixes don't work, consider:
- Fine-tune a smaller model (gpt-3.5-turbo) with specific examples
- Use function calling with strict JSON schemas
- Switch to Claude or other models with better instruction following
- Implement a multi-step approach with separate AI calls for each system

## Recommended Next Steps:

1. **Test with limit=10**: Force small limits on all tools
2. **Add JavaScript pre-processing**: Extract and standardize company names
3. **Implement Try/Catch**: Handle failed searches at workflow level
4. **Consider model switch**: If GPT-4 continues to ignore instructions

Would you like me to help implement any of these workflow-level solutions? 