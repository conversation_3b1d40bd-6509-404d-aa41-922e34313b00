# HubSpot Security Configuration

## CRITICAL: READ-ONLY ENFORCEMENT

### Tool Description Updates
Update your HubSpot tool descriptions to include:

**search_hubspot_companies:**
```
"This tool performs READ-ONLY searches of HubSpot companies. It uses POST method for complex search queries but CANNOT and <PERSON><PERSON><PERSON> NOT modify any data. Only use for retrieving company information."
```

**search_hubspot_deals:**
```
"This tool performs READ-ONLY searches of HubSpot deals. It uses POST method for complex search queries but CANNOT and WILL NOT modify any data. Only use for retrieving deal information."
```

### System Prompt Addition
Add this to your system prompt:

```
## HUBSPOT SECURITY RULES
- HubSpot tools are READ-ONLY - never attempt to modify data
- POST method is used only for complex search queries
- NEVER use these tools to create, update, or delete records
- If asked to modify HubSpot data, explain you can only read/search
```

### Alternative: Switch to GET Methods
If your HubSpot integration supports it, consider switching to GET-based search endpoints:
- `/crm/v3/objects/companies/search` (GET with query parameters)
- `/crm/v3/objects/deals/search` (GET with query parameters)

This would eliminate the POST method security concern entirely.

### Request Body Validation
Ensure your n8n HubSpot nodes only accept search-related JSON bodies:
```json
{
  "filterGroups": [...],
  "sorts": [...],
  "properties": [...],
  "limit": 50
}
```

No `create`, `update`, `delete`, or `patch` operations should be possible. 