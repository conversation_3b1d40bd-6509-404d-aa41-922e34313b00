{"name": "Two-Model Customer AI Agent", "nodes": [{"parameters": {"trigger": ["app_mention"], "channelId": {"__rl": true, "value": "C0959L8ETLH", "mode": "id"}}, "type": "n8n-nodes-base.slackTrigger", "typeVersion": 1, "position": [-220, 60], "id": "slack-trigger", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"promptType": "define", "text": "={{ $json.text }}", "options": {"systemMessage": "# REASONING MODEL - Task Planner\n\nYou are a reasoning agent that analyzes user requests and creates detailed execution plans.\n\n## Your Role\n1. Understand the user's request completely\n2. Determine which systems need to be queried (HubSpot, Zendesk, Xero)\n3. Create a DETAILED step-by-step execution plan\n4. Consider all edge cases and retry strategies\n\n## Output Format\nYou MUST output a JSON execution plan:\n```json\n{\n  \"request_summary\": \"Brief summary of what user wants\",\n  \"company_info\": {\n    \"name\": \"extracted company name\",\n    \"domain\": \"extracted domain if any\",\n    \"variations\": [\"possible name variations\"]\n  },\n  \"execution_steps\": [\n    {\n      \"step\": 1,\n      \"action\": \"search_hubspot_companies\",\n      \"parameters\": {\"search_term\": \"exact value\"},\n      \"fallback\": \"what to do if no results\",\n      \"extract\": \"what data to extract\"\n    }\n  ],\n  \"output_requirements\": {\n    \"verbosity\": \"detailed|summary|balanced\",\n    \"focus_areas\": [\"what to emphasize\"],\n    \"exclude\": [\"what to skip\"]\n  }\n}\n```\n\n## Available Actions\n- search_hubspot_companies\n- search_hubspot_deals\n- get_hubspot_deal_details\n- search_zendesk_organizations\n- search_zendesk_tickets\n- get_zendesk_ticket_comments\n- search_xero_contacts\n- search_xero_invoices\n- get_xero_invoice_details", "maxIterations": 1}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [100, 60], "id": "reasoning-agent", "name": "Reasoning Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o", "mode": "list"}, "options": {"temperature": 0.3, "responseFormat": "json_object"}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [100, 240], "id": "reasoning-model", "name": "GPT-4 Reasoning Model"}, {"parameters": {"promptType": "define", "text": "={{ $('Parse Execution Plan').item.json }}", "options": {"systemMessage": "# EXECUTION MODEL - Task Executor\n\nYou are executing a pre-planned series of API calls based on the execution plan provided.\n\n## Your Instructions\n1. Follow the execution plan EXACTLY\n2. Use the specified parameters for each tool call\n3. Handle responses and extract specified data\n4. If a step fails, follow the fallback instructions\n5. Format output according to output_requirements\n\n## Critical Rules\n- NO MARKDOWN in responses\n- Execute steps in order\n- Use exact parameter values from plan\n- Maximum 5 API calls total\n- Filter out RSD currency invoices", "maxIterations": 10}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [600, 60], "id": "execution-agent", "name": "Execution Agent"}, {"parameters": {"modelName": "models/gemini-2.5-flash", "options": {"maxOutputTokens": 8192, "temperature": 0.2}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [600, 240], "id": "execution-model", "name": "Gemini Flash Execution"}, {"parameters": {"jsCode": "// Parse the reasoning agent's output\nconst reasoningOutput = $('Reasoning Agent').item.json.output;\n\ntry {\n  // Extract JSON from the output if it contains other text\n  const jsonMatch = reasoningOutput.match(/```json\\n([\\s\\S]*?)\\n```/);\n  const jsonStr = jsonMatch ? jsonMatch[1] : reasoningOutput;\n  \n  const executionPlan = JSON.parse(jsonStr);\n  \n  return [{\n    json: executionPlan\n  }];\n} catch (error) {\n  // Fallback if parsing fails\n  return [{\n    json: {\n      error: 'Failed to parse execution plan',\n      raw: reasoningOutput\n    }\n  }];\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [350, 60], "id": "parse-plan", "name": "Parse Execution Plan"}], "connections": {"Slack Trigger": {"main": [[{"node": "Reasoning Agent", "type": "main", "index": 0}]]}, "Reasoning Agent": {"main": [[{"node": "Parse Execution Plan", "type": "main", "index": 0}]]}, "Parse Execution Plan": {"main": [[{"node": "Execution Agent", "type": "main", "index": 0}]]}, "GPT-4 Reasoning Model": {"ai_languageModel": [[{"node": "Reasoning Agent", "type": "ai_languageModel", "index": 0}]]}, "Gemini Flash Execution": {"ai_languageModel": [[{"node": "Execution Agent", "type": "ai_languageModel", "index": 0}]]}}}