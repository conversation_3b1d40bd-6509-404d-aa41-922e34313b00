{"name": "Marketing AI Agent", "nodes": [{"parameters": {"trigger": ["message"], "channelId": {"__rl": true, "value": "C09394XBA64", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.slackTrigger", "typeVersion": 1, "position": [-420, 0], "id": "187ce70b-9761-46b6-a573-866d8f12b3f0", "name": "<PERSON><PERSON><PERSON>", "webhookId": "afbc50d3-1996-4d5c-acc7-f166b860e2f0", "credentials": {"slackApi": {"id": "rXFEf0qBhJ1mIp12", "name": "Slack account 2"}}}, {"parameters": {"authentication": "oAuth2", "operation": "get", "contactId": {"__rl": true, "value": "={{ (($json.text || '').match(/hubspot\\.com\\/.*?\\/(\\d{6,14})(?:>|$|\\s)/) || [, ''])[1] }}", "mode": "id"}, "additionalFields": {}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [-60, 0], "id": "c675c297-52ee-40db-8878-dd8c85ea26a9", "name": "HubSpot", "credentials": {"hubspotOAuth2Api": {"id": "gvdd5eL7n4xNN1ph", "name": "HubSpot account"}}}, {"parameters": {"authentication": "oAuth2", "resource": "deal", "operation": "get", "dealId": {"__rl": true, "value": "={{ $json.dealId }}", "mode": "id"}, "filters": {}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [1020, -80], "id": "2025580c-8edc-4d41-a66f-ce3477a9537c", "name": "HubSpot1", "credentials": {"hubspotOAuth2Api": {"id": "gvdd5eL7n4xNN1ph", "name": "HubSpot account"}}, "onError": "continueRegularOutput"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"contactId\": \"{{ $json.vid }}\",\n  \"firstName\": \"{{ $json.properties.firstname.value }}\",\n  \"lastName\": \"{{ $json.properties.lastname.value }}\",\n  \"email\": \"{{ $json.properties.email.value }}\",\n  \"jobTitle\": \"{{ $json.properties.jobtitle.value }}\",\n  \"leadScore\": \"{{ $json.properties.lead_score.value }}\",\n  \"source\": \"{{ $json.properties.hs_analytics_source.value }}\",\n  \"campaign\": \"{{ $json.properties.hs_object_source_detail_1.value }}\",\n  \"companyId\": \"{{ $json.properties.associatedcompanyid.value }}\"\n}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [120, 100], "id": "2b914fd0-9a25-49f6-bf32-383c47966e9f", "name": "save contact info"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"dealId\": \"{{ $json.selectedDealId }}\"\n}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [820, -80], "id": "c53bfd20-f174-42d9-ade6-7e6bc457131a", "name": "save deal id"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "7d5440ee-a46d-4354-b1c8-6531ca17fdeb", "leftValue": "={{ $json.hasAnyDeal }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [600, 0], "id": "5bb64964-384e-46aa-b5cc-f69fc241ddfb", "name": "If"}, {"parameters": {"authentication": "oAuth2", "resource": "company", "operation": "get", "companyId": {"__rl": true, "value": "={{ $('HubSpot1').item.json.associations.associatedCompanyIds[0] }}", "mode": "id"}, "additionalFields": {}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [1380, -60], "id": "9d113344-0594-4952-8753-061dcf2643bd", "name": "HubSpot3", "alwaysOutputData": true, "credentials": {"hubspotOAuth2Api": {"id": "gvdd5eL7n4xNN1ph", "name": "HubSpot account"}}}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"dealId\": \"{{ $json.dealId }}\",\n  \"dealName\": \"{{ $json.properties.dealname.value }}\",\n  \"pipeline\": \"{{ $json.properties.pipeline.value }}\",\n  \"stageId\": \"{{ $json.properties.dealstage.value }}\",\n  \"probability\": \"{{ $json.properties.hs_deal_stage_probability.value }}\",\n  \"amountQuoted\": \"{{ $json.properties.potential_deal_amount.value }}\",\n  \"amountCalc\": \"{{ $json.properties.potential_deal_amount_calculator.value }}\",\n  \"amountForecast\": \"{{ $json.properties.hs_forecast_amount.value }}\",\n  \"isClosed\": \"{{ $json.properties.hs_is_closed.value }}\",\n  \"isClosedWon\": \"{{ $json.properties.hs_is_closed_won.value }}\",\n  \"createdDate\": \"{{ $json.properties.createdate.value }}\",\n  \"closeDate\": \"{{ $json.properties.closedate.value }}\",\n  \"ownerId\": \"{{ $json.properties.hubspot_owner_id.value }}\",\n  \"numAssociatedContacts\": \"{{ $json.properties.num_associated_contacts.value }}\",\n  \"daysToClose\": \"{{ $json.properties.days_to_close.value }}\",\n  \"proposedServices\": \"{{ $json.properties.proposed_services.value }}\"\n}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1200, -80], "id": "b3fc9610-85dd-4673-87d9-580f513f266c", "name": "save deal info"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"companyId\": \"{{ $json.companyId }}\",\n  \"companyName\": \"{{ $json.properties.name.value }}\",\n  \"domain\": \"{{ $json.properties.domain.value }}\",\n  \"industry\": \"{{ $json.properties.industry.value }}\",\n  \"phone\": \"{{ $json.properties.phone.value }}\",\n  \"address\": \"{{ $json.properties.address.value }}\",\n  \"city\": \"{{ $json.properties.city.value }}\",\n  \"state\": \"{{ $json.properties.state.value }}\",\n  \"zip\": \"{{ $json.properties.zip.value }}\",\n  \"country\": \"{{ $json.properties.country.value }}\",\n  \"numberOfEmployees\": \"{{ $json.properties.numberofemployees.value }}\",\n  \"annualRevenue\": \"{{ $json.properties.annualrevenue.value }}\",\n  \"website\": \"{{ $json.properties.website.value }}\",\n  \"platformType\": \"{{ $json.properties.platform_type.value }}\",\n  \"legacyVendor\": \"{{ $json.properties.legacy_vendor.value }}\"\n}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1680, 60], "id": "c41c7337-105c-438c-8e77-4109d0503429", "name": "save company info"}, {"parameters": {"authentication": "oAuth2", "resource": "company", "operation": "get", "companyId": {"__rl": true, "value": "={{ $('save contact info').item.json.companyId }}", "mode": "id"}, "additionalFields": {}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [920, 160], "id": "dd949b6e-5bcf-4e4d-bf56-747abb7d7276", "name": "HubSpot2", "credentials": {"hubspotOAuth2Api": {"id": "gvdd5eL7n4xNN1ph", "name": "HubSpot account"}}}, {"parameters": {"url": "=https://r.jina.ai/{{ $json.website }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1860, 60], "id": "6d2817e2-f030-4819-a63b-eae51db95c4d", "name": "website scrapping", "alwaysOutputData": false, "executeOnce": true, "onError": "continueRegularOutput"}, {"parameters": {"url": "https://real-time-web-search.p.rapidapi.com/search-advanced", "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "=\"{{ $('save contact info').item.json.firstName }} {{ $('save contact info').item.json.lastName }}\" \"{{ $('save company info').item.json.name }}\" OR \"{{ $('save company info').item.json.domain }}\" site:linkedin.com/in/"}, {"name": "limit", "value": "10"}, {"name": "start", "value": "0"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-RapidAPI-Key", "value": "**************************************************"}, {"name": "X-RapidAPI-Host", "value": "real-time-web-search.p.rapidapi.com"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2120, 60], "id": "b251ca52-2c09-43d3-ba4d-b3e832c9e0cd", "name": "Google > LinkedIn Search", "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "dc69a53c-054d-46f3-a493-a6734d01d564", "name": "Ln_URL", "value": "={{ ($json.data || []).find(r => r.url?.match(/^https:\\/\\/(www|[a-z]{2})\\.linkedin\\.com\\/in\\//))?.url || '' }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2360, 40], "id": "1aed3bbf-3fc0-4a7d-9e05-f0656ecab75f", "name": "save Ln URL"}, {"parameters": {"url": "https://scrapeninja.p.rapidapi.com/scrape-js", "sendQuery": true, "queryParameters": {"parameters": [{"name": "linkedin_url", "value": "={{ $json.Ln_URL }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-RapidAPI-Key", "value": "**************************************************"}, {"name": "X-RapidAPI-Host", "value": "scrapeninja.p.rapidapi.com"}, {"name": "Content-Type", "value": "application/json"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2580, 40], "id": "0d86f616-b238-4aa1-aaaa-bed128aa8872", "name": "scrape Ln", "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o", "mode": "list", "cachedResultName": "GPT-4O"}, "messages": {"values": [{"content": "You are a lead-enrichment assistant.\n\n- Produce exactly the 11 labelled lines shown below, one per line, in that order.  \n- After line 11, output a blank line, then one line that begins with \"Insight: \".  \n- Never add any extra lines or commentary.\n- CRITICAL: Use ONLY the actual data provided. Never make up names, URLs, or other details.\n\nFormatting rules\n- Do not write the words \"undefined\", \"null\", \"error\", or brackets if a value is missing.  \n- When data is missing, use these specific placeholders:\n  - Job Title: \"N/A (Position not found)\"  \n  - Company/Location: \"N/A (Location not available)\"  \n  - Website scraping errors: \"Website scraping failed (Code: [error_code])\"\n- The Deal line: If Stage is missing, write \"No active deal\" and leave the rest blank.\n- Currency fields must show a leading $ and end with \" USD\".  \n- Keep every line under 120 characters.\n- Use exact data provided - never substitute with generic examples.\n\nRequired layout:\n\n1. Lead: {Name}, {Job Title} at {Company Name}, {City}, {State/Region}.\n2. Industry: {Industry (LinkedIn)} / {Industry (HubSpot)}.\n3. Deal: {Stage}. PO Value: ${PO Value} USD, Forecast: ${Amount Forecast} USD, Quoted: ${Amount Quoted} USD, Calc: ${Amount Calculated} USD. Users: {Deal User Volume}. Services: {Proposed Services}.\n4. Employee Count: {LinkedIn employee count} (LinkedIn, {LinkedIn range}). Annual Revenue: ${Annual Revenue} USD. Company Users: {Company User Volume}.\n5. Source: {Source} from \"{Campaign}\" campaign.\n6. Website Note: {website summary or error message}.\n7. LinkedIn Profile: {LinkedIn URL}. Headline: {LinkedIn headline}.\n8. LinkedIn Snapshot: {two concise sentences combining About, mission, follower count, standout skills}.\n9. Current Role: {current role}. Previous Role: {previous role}.\n10. Education: {degree 1}; {degree 2}.\n11. Languages: {languages list}. LinkedIn Company Profile: {company LinkedIn URL}.\n\nDeal Amount Priority: Use PO Value as primary amount (highest priority), then Forecast, then Quoted, then Calculated as fallback. Always show all available amounts but prioritize PO Value in your insight.\n\nInsight: Write one useful sentence (25 words maximum) that gives our sales rep actionable information about this lead. Use their LinkedIn data, company details, or professional background. If no clear data archiving connection exists, provide other valuable context about their role, influence, or business focus. End with a period.\n\nExample format (using actual data):\n1. Lead: Lisa Delapo, IT Director at Union School District, San Jose, California.\n2. Industry: Education / Education.\n3. Deal: No active deal. PO Value: $0 USD, Forecast: $0 USD, Quoted: $0 USD, Calc: $0 USD. Users: Not available. Services: Not available.\n4. Employee Count: 647 (LinkedIn, 501-1000). Annual Revenue: $25,000,000 USD. Company Users: 1,200.\n5. Source: OFFLINE from \"2025 Q2 School Districts campaign regular Part18.csv\" campaign.\n6. Website Note: Educational technology district focused on student-centered learning environments.\n7. LinkedIn Profile: https://www.linkedin.com/in/lisadelapo/. Headline: Experienced technology leader.\n8. LinkedIn Snapshot: Visionary technology leader with cybersecurity focus. 1,459 followers with expertise in educational innovation.\n9. Current Role: Director of Information Technology @ Union School District (2019-Present). Previous Role: Innovator in Residence @ Krause Center (2017-2019).\n10. Education: Graduate Certificate in Network Management – Keller Graduate School (2011-2012); MAEd in Instructional Leadership – Argosy University (2007-2009).\n11. Languages: English. LinkedIn Company Profile: https://www.linkedin.com/company/union-school-district.\n\nInsight: Lisa has 1,459 LinkedIn followers and moved from innovation to IT leadership, suggesting she values forward-thinking technology solutions.", "role": "system"}, {"content": "=Name: {{ $json.name }}\nJob Title: {{ $json.jobTitle }}\nSource: {{ $json.source }}\nCampaign: {{ $json.campaign }}\n\nDeal Stage: {{ $json.stage }}\nPO Value: {{ $json.poValue }}\nAmount Forecast: {{ $json.amountForecast }}\nAmount Quoted: {{ $json.amountQuoted }}\nAmount Calculated: {{ $json.amountCalc }}\nDeal User Volume: {{ $json.userVolume }}\nProposed Services: {{ $json.proposedServices }}\n\nCompany Name: {{ $json.companyName }}\nIndustry (HubSpot): {{ $json.industryHS }}\nIndustry (LinkedIn): {{ $json.industryLI }}\nCity: {{ $json.city }}\nState: {{ $json.state }}\nCountry: {{ $json.country }}\n\nEmployees (HubSpot): {{ $json.employeesHS }}\nEmployees (LinkedIn): {{ $json.employeesLI }} ({{ $json.employeesRangeLI }})\nAnnual Revenue: {{ $json.revenue }}\nCompany User Volume: {{ $json.userVolumeCompany }}\n\nWebsite Notes: {{ $json.websiteNote }}\n\nLinkedIn Profile: {{ $json.linkedinUrl }}\nLinkedIn Headline: {{ $json.headline }}\nLinkedIn About: {{ $json.about }}\nFollower Count: {{ $json.followerCount }}\n\nCurrent Role: {{ $json.currentRole }}\nPrevious Role: {{ $json.previousRole }}\n\nEducation 1: {{ $json.education1 }}\nEducation 2: {{ $json.education2 }}\n\nLanguages: {{ $json.languages }}\nCompany LinkedIn URL: {{ $json.companyLinkedinUrl }}"}]}, "options": {"temperature": 0.2}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [3060, 40], "id": "8a35be48-1489-470b-bca8-a29aaaf33f02", "name": "OpenAI", "credentials": {"openAiApi": {"id": "fvvf1IRjKSg6jNkv", "name": "OpenAi account"}}}, {"parameters": {"select": "channel", "channelId": {"__rl": true, "value": "={{ $('Slack Trigger').item.json.channel }}", "mode": "id"}, "text": "={{ $json.message.content }}", "otherOptions": {"includeLinkToWorkflow": false, "thread_ts": {"replyValues": {"thread_ts": "={{ $('Slack Trigger').item.json.ts }}"}}}}, "type": "n8n-nodes-base.slack", "typeVersion": 2.3, "position": [3460, 40], "id": "5d269150-14a4-4868-8d63-b23146a29624", "name": "<PERSON><PERSON>ck", "webhookId": "c0f822aa-77bd-422a-8719-52c1e01c407d", "credentials": {"slackApi": {"id": "rXFEf0qBhJ1mIp12", "name": "Slack account 2"}}}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"dealId\": \"{{ $json.dealId }}\",\n  \"dealName\": \"{{ $json.properties.dealname.value }}\",\n  \"pipeline\": \"{{ $json.properties.pipeline.value }}\",\n  \"stageId\": \"{{ $json.properties.dealstage.value }}\",\n  \"probability\": \"{{ $json.properties.hs_deal_stage_probability.value }}\",\n  \"amountQuoted\": \"{{ $json.properties.potential_deal_amount.value }}\",\n  \"amountCalc\": \"{{ $json.properties.potential_deal_amount_calculator.value }}\",\n  \"isClosed\": \"{{ $json.properties.hs_is_closed.value }}\",\n  \"isClosedWon\": \"{{ $json.properties.hs_is_closed_won.value }}\",\n  \"createdDate\": \"{{ $json.properties.createdate.value }}\",\n  \"closeDate\": \"{{ $json.properties.closedate.value }}\",\n  \"ownerId\": \"{{ $json.properties.hubspot_owner_id.value }}\",\n  \"numAssociatedContacts\": \"{{ $json.properties.num_associated_contacts.value }}\",\n  \"daysToClose\": \"{{ $json.properties.days_to_close.value }}\"\n}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1240, 180], "id": "00a3c7da-b237-4daa-bb5a-eea4eefd8391", "name": "save deal info1", "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "const nd = n => {\n\ttry { return $(n).first().json || {}; }\n\tcatch { return {}; }\n};\nconst v = (o, p, d = '') =>\n\tp.split('.').reduce((a, k) => (a ?? {})[k], o) ?? d;\n\nconst contact  = nd('save contact info');\nconst site     = nd('website scrapping');\nconst lnRaw    = nd('scrape Ln');\nconst ln       = lnRaw.data || {};\n\nconst dealTrue = nd('save deal info');\nconst dealFalse = nd('save deal info1');\nconst deal = (Object.keys(dealTrue).length > 0) ? dealTrue : dealFalse;\n\nconst companyTrue = nd('HubSpot3');\nconst companyFalse = nd('HubSpot2');\nconst companyRaw = (Object.keys(companyTrue).length > 0) ? companyTrue : companyFalse;\n\n// Helper function to clean up website scraping errors\nconst cleanWebsiteNote = (data) => {\n\tif (!data || data === '') return 'Website not available';\n\tif (data.includes('AxiosError') || data.includes('Error')) {\n\t\tconst codeMatch = data.match(/status code (\\d+)/);\n\t\treturn codeMatch ? `Website scraping failed (Code: ${codeMatch[1]})` : 'Website scraping failed';\n\t}\n\treturn data;\n};\n\n// Helper to handle undefined/empty fields with better placeholders\nconst cleanField = (value, context = 'general') => {\n\tif (!value || value === '' || value === 'undefined') {\n\t\tswitch(context) {\n\t\t\tcase 'jobTitle': return 'Position not found';\n\t\t\tcase 'company': return 'Company not available';\n\t\t\tcase 'location': return 'Location not available';\n\t\t\tcase 'industry': return 'Industry not specified';\n\t\t\tcase 'revenue': return 'Revenue not disclosed';\n\t\t\tcase 'employees': return 'Employee count not available';\n\t\t\tcase 'campaign': return 'Campaign not specified';\n\t\t\tdefault: return 'Not available';\n\t\t}\n\t}\n\treturn value;\n};\n\nconst out = {\n\tname:          `${v(contact,'firstName')} ${v(contact,'lastName')}`.trim(),\n\tjobTitle:      cleanField(v(contact,'jobTitle'), 'jobTitle'),\n\tsource:        v(contact,'source'),\n\tcampaign:      cleanField(v(contact,'campaign'), 'campaign'),\n\n\t// Updated deal fields with new additions\n\tstage:         v(deal,'stageId'),\n\tdealName:      v(deal,'dealName'),\n\tpipeline:      v(deal,'pipeline'),\n\tprobability:   v(deal,'probability'),\n\tamountQuoted:  v(deal,'amountQuoted'),\n\tamountCalc:    v(deal,'amountCalc'),\n\tamountForecast: v(deal,'amountForecast'),\n\tisClosed:      v(deal,'isClosed'),\n\tisClosedWon:   v(deal,'isClosedWon'),\n\tcreatedDate:   v(deal,'createdDate'),\n\tcloseDate:     v(deal,'closeDate'),\n\townerId:       v(deal,'ownerId'),\n\tnumAssociatedContacts: v(deal,'numAssociatedContacts'),\n\tdaysToClose:   v(deal,'daysToClose'),\n\tproposedServices: v(deal,'proposedServices'),\n\tuserVolume:    v(deal,'userVolume'),\n\n\tcompanyName:        cleanField(v(companyRaw,'properties.name.value'), 'company'),\n\tindustryHS:         cleanField(v(companyRaw,'properties.industry.value'), 'industry'),\n\tindustryLI:         cleanField(v(ln,'company_industry'), 'industry'),\n\tcity:               cleanField(v(companyRaw,'properties.city.value'), 'location'),\n\tstate:              cleanField(v(companyRaw,'properties.state.value'), 'location'),\n\tcountry:            cleanField(v(companyRaw,'properties.country.value'), 'location'),\n\temployeesHS:        cleanField(v(companyRaw,'properties.numberofemployees.value'), 'employees'),\n\temployeesLI:        cleanField(v(ln,'company_employee_count'), 'employees'),\n\temployeesRangeLI:   cleanField(v(ln,'company_employee_range'), 'employees'),\n\trevenue:            cleanField(v(companyRaw,'properties.annualrevenue.value'), 'revenue'),\n\tpoValue:            cleanField(v(companyRaw,'properties.po_value.value'), 'revenue'),\n\tuserVolumeCompany:  cleanField(v(companyRaw,'properties.user_volume.value'), 'employees'),\n\n\twebsiteNote:  cleanWebsiteNote(v(site,'data') || v(site,'error.stack')),\n\n\tlinkedinUrl:   v(ln,'linkedin_url'),\n\theadline:      v(ln,'headline'),\n\tabout:         v(ln,'about'),\n\tfollowerCount: v(ln,'follower_count'),\n\n\tcurrentRole: (() => {\n\t\tconst t = v(ln,'experiences.0.title');\n\t\treturn t ? `${t} @ ${v(ln,'experiences.0.company')} (${v(ln,'experiences.0.date_range')})` : '';\n\t})(),\n\n\tpreviousRole: (() => {\n\t\tconst t = v(ln,'experiences.1.title');\n\t\treturn t ? `${t} @ ${v(ln,'experiences.1.company')} (${v(ln,'experiences.1.date_range')})` : '';\n\t})(),\n\n\teducation1: (() => {\n\t\tconst d = v(ln,'educations.0.degree');\n\t\treturn d ? `${d} in ${v(ln,'educations.0.field_of_study')} – ${v(ln,'educations.0.school')} (${v(ln,'educations.0.date_range')})` : '';\n\t})(),\n\n\teducation2: (() => {\n\t\tconst d = v(ln,'educations.1.degree');\n\t\treturn d ? `${d} in ${v(ln,'educations.1.field_of_study')} – ${v(ln,'educations.1.school')} (${v(ln,'educations.1.date_range')})` : '';\n\t})(),\n\n\tlanguages: (() => {\n\t\tconst arr = v(ln,'languages', []);\n\t\treturn Array.isArray(arr)\n\t\t\t? arr.map(l => l.name + (l.proficiency ? ` (${l.proficiency})` : '')).join(', ')\n\t\t\t: '';\n\t})(),\n\n\tcompanyLinkedinUrl: cleanField(v(ln,'company_linkedin_url')),\n};\n\nreturn [{ json: out }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2780, 40], "id": "62fe8c1f-e7ee-4c1b-9dc1-807e68d3ee25", "name": "Code"}, {"parameters": {"url": "=https://api.hubapi.com/crm/v4/objects/companies/{{ $('save contact info').item.json.companyId }}/associations/deals?limit=5", "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [280, 260], "id": "ec81042a-6efb-44f7-845e-651a723833b2", "name": "get company deals", "credentials": {"hubspotOAuth2Api": {"id": "gvdd5eL7n4xNN1ph", "name": "HubSpot account"}}}, {"parameters": {"url": "=https://api.hubapi.com/crm/v4/objects/contacts/{{ $('save contact info').item.json.contactId }}/associations/deals?limit=1", "authentication": "predefinedCredentialType", "nodeCredentialType": "hubspotOAuth2Api", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [520, 260], "id": "********-802d-495f-b824-d6488baccb3f", "name": "get dealid", "credentials": {"hubspotOAuth2Api": {"id": "gvdd5eL7n4xNN1ph", "name": "HubSpot account"}}}, {"parameters": {"jsCode": "const contactDeals = $('get dealid').first().json;\nconst companyDeals = $('get company deals').first().json;\n\nlet decision = {\n    selectedDealId: null,\n    dealSource: 'none',\n    hasAnyDeal: false\n};\n\n// Check contact deals first (higher priority)\nif (contactDeals.results && contactDeals.results[0]?.toObjectId) {\n    decision.selectedDealId = contactDeals.results[0].toObjectId;\n    decision.dealSource = 'contact';\n    decision.hasAnyDeal = true;\n} \n// If no contact deals, check company deals\nelse if (companyDeals.results && companyDeals.results[0]?.toObjectId) {\n    decision.selectedDealId = companyDeals.results[0].toObjectId;\n    decision.dealSource = 'company';\n    decision.hasAnyDeal = true;\n}\n\nreturn [{ json: decision }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [740, 260], "id": "6bad389e-573d-4e65-9d1d-d13bf779f551", "name": "Decide best deal"}], "pinData": {}, "connections": {"Slack Trigger": {"main": [[{"node": "HubSpot", "type": "main", "index": 0}]]}, "HubSpot": {"main": [[{"node": "save contact info", "type": "main", "index": 0}]]}, "save contact info": {"main": [[{"node": "get company deals", "type": "main", "index": 0}]]}, "save deal id": {"main": [[{"node": "HubSpot1", "type": "main", "index": 0}]]}, "HubSpot1": {"main": [[{"node": "save deal info", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "save deal id", "type": "main", "index": 0}], [{"node": "HubSpot2", "type": "main", "index": 0}]]}, "save deal info": {"main": [[{"node": "HubSpot3", "type": "main", "index": 0}]]}, "HubSpot3": {"main": [[{"node": "save company info", "type": "main", "index": 0}]]}, "HubSpot2": {"main": [[{"node": "save deal info1", "type": "main", "index": 0}]]}, "save company info": {"main": [[{"node": "website scrapping", "type": "main", "index": 0}]]}, "website scrapping": {"main": [[{"node": "Google > LinkedIn Search", "type": "main", "index": 0}]]}, "Google > LinkedIn Search": {"main": [[{"node": "save Ln URL", "type": "main", "index": 0}]]}, "save Ln URL": {"main": [[{"node": "scrape Ln", "type": "main", "index": 0}]]}, "scrape Ln": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "<PERSON><PERSON>ck", "type": "main", "index": 0}]]}, "save deal info1": {"main": [[{"node": "save company info", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "get company deals": {"main": [[{"node": "get dealid", "type": "main", "index": 0}]]}, "get dealid": {"main": [[{"node": "Decide best deal", "type": "main", "index": 0}]]}, "Decide best deal": {"main": [[{"node": "If", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "6508aa32-c6b0-4840-85a9-496178a864b4", "meta": {"templateCredsSetupCompleted": true, "instanceId": "49d173457389c9c4ecf38679bf3dd6896dcb6d1131139a16c229813972afbb28"}, "id": "ezpGhTl6SZnSah7C", "tags": []}