{"name": "Process Zoom recordings 2 - SalesDemoAgent", "flow": [{"id": 3, "module": "google-sheets:filterRows", "version": 2, "parameters": {"__IMTCONN__": 2160110}, "mapper": {"from": "drive", "limit": "15", "filter": [[{"a": "L", "o": "notexist"}]], "sheetId": "Sheet1", "sortOrder": "asc", "spreadsheetId": "1dotBroTMKjiSErdtnwJUACwsvQwplnLWY4r_TV_Tvv4", "tableFirstRow": "A1:CZ1", "includesHeaders": true, "valueRenderOption": "FORMATTED_VALUE", "dateTimeRenderOption": "FORMATTED_STRING"}, "metadata": {"designer": {"x": 0, "y": 0}, "restore": {"expect": {"from": {"label": "Select from My Drive"}, "orderBy": {"mode": "chose"}, "sheetId": {"mode": "chose", "label": "Sheet1"}, "sortOrder": {"mode": "chose", "label": "Ascending"}, "spreadsheetId": {"mode": "chose", "label": "Zoom Recordings - Since September"}, "tableFirstRow": {"label": "A-CZ"}, "includesHeaders": {"mode": "chose", "label": "Yes"}, "valueRenderOption": {"mode": "chose", "label": "Formatted value"}, "dateTimeRenderOption": {"mode": "chose", "label": "Formatted string"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "from", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["drive", "share"]}}, {"name": "valueRenderOption", "type": "select", "label": "Value render option", "validate": {"enum": ["FORMATTED_VALUE", "UNFORMATTED_VALUE", "FORMULA"]}}, {"name": "dateTimeRenderOption", "type": "select", "label": "Date and time render option", "validate": {"enum": ["SERIAL_NUMBER", "FORMATTED_STRING"]}}, {"name": "limit", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Limit"}, {"name": "spreadsheetId", "type": "select", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "tableFirstRow", "type": "select", "label": "Column range", "required": true, "validate": {"enum": ["A1:Z1", "A1:BZ1", "A1:CZ1", "A1:DZ1", "A1:MZ1", "A1:ZZ1", "A1:AZZ1", "A1:BZZ1", "A1:CZZ1", "A1:DZZ1", "A1:MZZ1", "A1:ZZZ1"]}}, {"name": "filter", "type": "filter", "label": "Filter", "options": "rpc://google-sheets/2/rpcGetFilterKeys?includesHeaders=true"}, {"name": "orderBy", "type": "select", "label": "Order by"}, {"name": "sortOrder", "type": "select", "label": "Sort order", "validate": {"enum": ["asc", "desc"]}}], "interface": [{"name": "__IMTLENGTH__", "label": "Total number of bundles", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "__IMTINDEX__", "label": "Bundle order position", "type": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "__ROW_NUMBER__", "type": "number", "label": "Row number"}, {"name": "__SPREADSHEET_ID__", "type": "text", "label": "Spreadsheet ID"}, {"name": "__SHEET__", "type": "text", "label": "Sheet"}, {"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "transcription (L)"}, {"name": "12", "type": "text", "label": "speaker version (M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}, {"name": "26", "type": "text", "label": "(AA)"}, {"name": "27", "type": "text", "label": "(AB)"}, {"name": "28", "type": "text", "label": "(AC)"}, {"name": "29", "type": "text", "label": "(AD)"}, {"name": "30", "type": "text", "label": "(AE)"}, {"name": "31", "type": "text", "label": "(AF)"}, {"name": "32", "type": "text", "label": "(AG)"}, {"name": "33", "type": "text", "label": "(AH)"}, {"name": "34", "type": "text", "label": "(AI)"}, {"name": "35", "type": "text", "label": "(AJ)"}, {"name": "36", "type": "text", "label": "(AK)"}, {"name": "37", "type": "text", "label": "(AL)"}, {"name": "38", "type": "text", "label": "(AM)"}, {"name": "39", "type": "text", "label": "(AN)"}, {"name": "40", "type": "text", "label": "(AO)"}, {"name": "41", "type": "text", "label": "(AP)"}, {"name": "42", "type": "text", "label": "(AQ)"}, {"name": "43", "type": "text", "label": "(AR)"}, {"name": "44", "type": "text", "label": "(AS)"}, {"name": "45", "type": "text", "label": "(AT)"}, {"name": "46", "type": "text", "label": "(AU)"}, {"name": "47", "type": "text", "label": "(AV)"}, {"name": "48", "type": "text", "label": "(AW)"}, {"name": "49", "type": "text", "label": "(AX)"}, {"name": "50", "type": "text", "label": "(AY)"}, {"name": "51", "type": "text", "label": "(AZ)"}, {"name": "52", "type": "text", "label": "(BA)"}, {"name": "53", "type": "text", "label": "(BB)"}, {"name": "54", "type": "text", "label": "(BC)"}, {"name": "55", "type": "text", "label": "(BD)"}, {"name": "56", "type": "text", "label": "(BE)"}, {"name": "57", "type": "text", "label": "(BF)"}, {"name": "58", "type": "text", "label": "(BG)"}, {"name": "59", "type": "text", "label": "(BH)"}, {"name": "60", "type": "text", "label": "(BI)"}, {"name": "61", "type": "text", "label": "(BJ)"}, {"name": "62", "type": "text", "label": "(BK)"}, {"name": "63", "type": "text", "label": "(BL)"}, {"name": "64", "type": "text", "label": "(BM)"}, {"name": "65", "type": "text", "label": "(BN)"}, {"name": "66", "type": "text", "label": "(BO)"}, {"name": "67", "type": "text", "label": "(BP)"}, {"name": "68", "type": "text", "label": "(BQ)"}, {"name": "69", "type": "text", "label": "(BR)"}, {"name": "70", "type": "text", "label": "(BS)"}, {"name": "71", "type": "text", "label": "(BT)"}, {"name": "72", "type": "text", "label": "(BU)"}, {"name": "73", "type": "text", "label": "(BV)"}, {"name": "74", "type": "text", "label": "(BW)"}, {"name": "75", "type": "text", "label": "(BX)"}, {"name": "76", "type": "text", "label": "(BY)"}, {"name": "77", "type": "text", "label": "(BZ)"}, {"name": "78", "type": "text", "label": "(CA)"}, {"name": "79", "type": "text", "label": "(CB)"}, {"name": "80", "type": "text", "label": "(CC)"}, {"name": "81", "type": "text", "label": "(CD)"}, {"name": "82", "type": "text", "label": "(CE)"}, {"name": "83", "type": "text", "label": "(CF)"}, {"name": "84", "type": "text", "label": "(CG)"}, {"name": "85", "type": "text", "label": "(CH)"}, {"name": "86", "type": "text", "label": "(CI)"}, {"name": "87", "type": "text", "label": "(CJ)"}, {"name": "88", "type": "text", "label": "(CK)"}, {"name": "89", "type": "text", "label": "(CL)"}, {"name": "90", "type": "text", "label": "(CM)"}, {"name": "91", "type": "text", "label": "(CN)"}, {"name": "92", "type": "text", "label": "(CO)"}, {"name": "93", "type": "text", "label": "(CP)"}, {"name": "94", "type": "text", "label": "(CQ)"}, {"name": "95", "type": "text", "label": "(CR)"}, {"name": "96", "type": "text", "label": "(CS)"}, {"name": "97", "type": "text", "label": "(CT)"}, {"name": "98", "type": "text", "label": "(CU)"}, {"name": "99", "type": "text", "label": "(CV)"}, {"name": "100", "type": "text", "label": "(CW)"}, {"name": "101", "type": "text", "label": "(CX)"}, {"name": "102", "type": "text", "label": "(CY)"}, {"name": "103", "type": "text", "label": "(CZ)"}]}}, {"id": 6, "module": "builtin:BasicFeeder", "version": 1, "parameters": {}, "mapper": {"array": "{{3.`8`}}"}, "metadata": {"designer": {"x": 300, "y": 0}, "restore": {"expect": {"array": {"mode": "edit"}}}, "expect": [{"mode": "edit", "name": "array", "spec": [], "type": "array", "label": "Array"}]}}, {"id": 8, "module": "http:ActionSendData", "version": 3, "parameters": {"handleErrors": true, "useNewZLibDeCompress": true}, "mapper": {"ca": "", "qs": [], "url": "https://api.deepgram.com/v1/listen?summarize=v2&smart_format=true&punctuate=true&paragraphs=true&utterances=true&diarize=true&filler_words=true&language=en&model=nova-3", "data": "{\n     \"url\": \"{{6.value}}?access_token=eyJzdiI6IjAwMDAwMiIsImFsZyI6IkhTNTEyIiwidiI6IjIuMCIsImtpZCI6ImRiY2MzNDQyLWEzZTctNDY1Mi1hYTA2LWFkOTc2NDZmNTkxOCJ9.eyJ2ZXIiOjEwLCJhdWlkIjoiZDZmZGQ1MzIxOTdlY2E2NmI2MzhjYTQyMmE2MzliODUyOTc2YjE0ZDhmODYyNjgxMTA5NTAxMjEwZjNmYjI3ZCIsImNvZGUiOiJ0OEVvS3IybzZjTElCTnRnR3ZiVHpDcWxYWU9ZQ0lBN2ciLCJpc3MiOiJ6bTpjaWQ6MjFvZHdWZ1FmTzE3dEtUWnBIcFdnIiwiZ25vIjowLCJ0eXBlIjowLCJ0aWQiOjAsImF1ZCI6Imh0dHBzOi8vb2F1dGguem9vbS51cyIsInVpZCI6IldfOXdUcVFBUm51OWVwVmNFWXJUdmciLCJuYmYiOjE3NDE3OTc5ODksImV4cCI6MTc0MTgwMTU4OSwiaWF0IjoxNzQxNzk3OTg5LCJhaWQiOiJGR2NBbFJFcFFnR2ZZOEc5d002cXN3In0.PWNzRebVSBZpRPGJCy0cdkAjNtE5hYxrCNsdDNXV9zYYxuLTb8prN01Ye9jY7KxDdXPj4jOW1TdzLkPRyJkuwA\"\n}", "gzip": true, "method": "post", "headers": [{"name": "Authorization", "value": "Token 2fb9b5aa1724411fd480a3fc2927d057420b51ce"}], "timeout": "", "useMtls": false, "authPass": "", "authUser": "", "bodyType": "raw", "contentType": "application/json", "serializeUrl": false, "shareCookies": false, "parseResponse": true, "followRedirect": true, "useQuerystring": false, "followAllRedirects": false, "rejectUnauthorized": true}, "metadata": {"designer": {"x": 600, "y": 0}, "restore": {"expect": {"qs": {"mode": "chose"}, "method": {"mode": "chose", "label": "POST"}, "headers": {"mode": "chose", "items": [null]}, "bodyType": {"label": "Raw"}, "contentType": {"label": "JSON (application/json)"}}}, "parameters": [{"name": "handleErrors", "type": "boolean", "label": "Evaluate all states as errors (except for 2xx and 3xx )", "required": true}, {"name": "useNewZLibDeCompress", "type": "hidden"}], "expect": [{"name": "url", "type": "url", "label": "URL", "required": true}, {"name": "serializeUrl", "type": "boolean", "label": "Serialize URL", "required": true}, {"name": "method", "type": "select", "label": "Method", "required": true, "validate": {"enum": ["get", "head", "post", "put", "patch", "delete", "options"]}}, {"name": "headers", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Headers"}, {"name": "qs", "spec": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "value", "type": "text", "label": "Value"}], "type": "array", "label": "Query String"}, {"name": "bodyType", "type": "select", "label": "Body type", "validate": {"enum": ["raw", "x_www_form_urlencoded", "multipart_form_data"]}}, {"name": "parseResponse", "type": "boolean", "label": "Parse response", "required": true}, {"name": "authUser", "type": "text", "label": "User name"}, {"name": "authPass", "type": "password", "label": "Password"}, {"name": "timeout", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Timeout", "validate": {"max": 300, "min": 1}}, {"name": "shareCookies", "type": "boolean", "label": "Share cookies with other HTTP modules", "required": true}, {"name": "ca", "type": "cert", "label": "Self-signed certificate"}, {"name": "rejectUnauthorized", "type": "boolean", "label": "Reject connections that are using unverified (self-signed) certificates", "required": true}, {"name": "followRedirect", "type": "boolean", "label": "Follow redirect", "required": true}, {"name": "useQuerystring", "type": "boolean", "label": "Disable serialization of multiple same query string keys as arrays", "required": true}, {"name": "gzip", "type": "boolean", "label": "Request compressed content", "required": true}, {"name": "useMtls", "type": "boolean", "label": "Use Mutual TLS", "required": true}, {"name": "contentType", "type": "select", "label": "Content type", "validate": {"enum": ["text/plain", "application/json", "application/xml", "text/xml", "text/html", "custom"]}}, {"name": "data", "type": "buffer", "label": "Request content"}, {"name": "followAllRedirects", "type": "boolean", "label": "Follow all redirect", "required": true}]}}, {"id": 11, "module": "json:TransformToJSON", "version": 1, "parameters": {"space": ""}, "mapper": {"object": "{{8.data.results.channels[].alternatives[].paragraphs.transcript}}"}, "metadata": {"designer": {"x": 914, "y": -10}, "restore": {"parameters": {"space": {"label": "Empty"}}}, "parameters": [{"name": "space", "type": "select", "label": "Indentation", "validate": {"enum": ["tab", "2", "4"]}}], "expect": [{"name": "object", "type": "any", "label": "Object"}]}}, {"id": 17, "module": "google-docs:createADocument", "version": 1, "parameters": {"__IMTCONN__": 2160110}, "mapper": {"name": "{{3.`1`}} Transcript", "footer": false, "header": false, "content": "{{11.json}}", "folderId": "/1iwdnVaft0jqsV6tHElCJ6rHC9_DDAZl-/13fB_XKfPnfF2Y6vYl0a1FozUuVP6KNR6", "destination": "team", "sharedDrive": "0AMSoJI76PCG3Uk9PVA", "useDomainAdminAccess": false}, "metadata": {"designer": {"x": 1214, "y": -10}, "restore": {"expect": {"folderId": {"mode": "chose", "path": ["Jatheon Demo Agent", "JSON Transcripts for Agent"]}, "destination": {"label": "Google Shared Drive"}, "sharedDrive": {"label": "Operations"}, "useDomainAdminAccess": {"label": "No"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "name", "type": "text", "label": "Name", "required": true}, {"name": "content", "type": "text", "label": "Content", "required": true}, {"name": "destination", "type": "select", "label": "Choose a Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "header", "type": "boolean", "label": "Insert a Header", "required": true}, {"name": "footer", "type": "boolean", "label": "Insert a Footer", "required": true}, {"name": "useDomainAdminAccess", "type": "select", "label": "Use Domain Admin Access", "required": true, "validate": {"enum": [true, false]}}, {"name": "sharedDrive", "type": "select", "label": "Shared Drive", "required": true}, {"name": "folderId", "type": "folder", "label": "New Document's Location", "required": true}]}}, {"id": 10, "module": "google-sheets:updateRow", "version": 2, "parameters": {"__IMTCONN__": 2160110}, "mapper": {"from": "drive", "mode": "select", "values": {"11": "{{17.webViewLink}}", "12": "{{8.data.results.summary.short}}"}, "sheetId": "Sheet1", "rowNumber": "{{3.`__ROW_NUMBER__`}}", "spreadsheetId": "/1dotBroTMKjiSErdtnwJUACwsvQwplnLWY4r_TV_Tvv4", "includesHeaders": true, "valueInputOption": "USER_ENTERED"}, "metadata": {"designer": {"x": 1548, "y": -12}, "restore": {"expect": {"from": {"label": "My Drive"}, "mode": {"label": "Search by path"}, "sheetId": {"label": "Sheet1"}, "spreadsheetId": {"path": ["Zoom Recordings - Since September"]}, "includesHeaders": {"label": "Yes", "nested": [{"name": "values", "spec": [{"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "transcription (L)"}, {"name": "12", "type": "text", "label": "speaker version (M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}, "valueInputOption": {"mode": "chose", "label": "User entered"}}, "parameters": {"__IMTCONN__": {"data": {"scoped": "true", "connection": "google"}, "label": "My Google connection (<EMAIL>)"}}}, "parameters": [{"name": "__IMTCONN__", "type": "account:google", "label": "Connection", "required": true}], "expect": [{"name": "mode", "type": "select", "label": "Search Method", "required": true, "validate": {"enum": ["select", "fromAll", "map"]}}, {"name": "valueInputOption", "type": "select", "label": "Value input option", "validate": {"enum": ["USER_ENTERED", "RAW"]}}, {"name": "from", "type": "select", "label": "Drive", "required": true, "validate": {"enum": ["drive", "share", "team"]}}, {"name": "spreadsheetId", "type": "file", "label": "Spreadsheet ID", "required": true}, {"name": "sheetId", "type": "select", "label": "Sheet Name", "required": true}, {"name": "rowNumber", "type": "<PERSON><PERSON><PERSON><PERSON>", "label": "Row number", "required": true}, {"name": "includesHeaders", "type": "select", "label": "Table contains headers", "required": true, "validate": {"enum": [true, false]}}, {"name": "values", "spec": [{"name": "0", "type": "text", "label": "topic (A)"}, {"name": "1", "type": "text", "label": "main_meeting_id (B)"}, {"name": "2", "type": "text", "label": "start_time (C)"}, {"name": "3", "type": "text", "label": "recording_id (D)"}, {"name": "4", "type": "text", "label": "recording_type (E)"}, {"name": "5", "type": "text", "label": "file_type (F)"}, {"name": "6", "type": "text", "label": "recording_meeting_id (G)"}, {"name": "7", "type": "text", "label": "play_url (H)"}, {"name": "8", "type": "text", "label": "download_url (I)"}, {"name": "9", "type": "text", "label": "duration (J)"}, {"name": "10", "type": "text", "label": "host email (K)"}, {"name": "11", "type": "text", "label": "transcription (L)"}, {"name": "12", "type": "text", "label": "speaker version (M)"}, {"name": "13", "type": "text", "label": "(N)"}, {"name": "14", "type": "text", "label": "(O)"}, {"name": "15", "type": "text", "label": "(P)"}, {"name": "16", "type": "text", "label": "(Q)"}, {"name": "17", "type": "text", "label": "(R)"}, {"name": "18", "type": "text", "label": "(S)"}, {"name": "19", "type": "text", "label": "(T)"}, {"name": "20", "type": "text", "label": "(U)"}, {"name": "21", "type": "text", "label": "(V)"}, {"name": "22", "type": "text", "label": "(W)"}, {"name": "23", "type": "text", "label": "(X)"}, {"name": "24", "type": "text", "label": "(Y)"}, {"name": "25", "type": "text", "label": "(Z)"}], "type": "collection", "label": "Values"}]}}], "metadata": {"instant": false, "version": 1, "scenario": {"roundtrips": 1, "maxErrors": 3, "autoCommit": true, "autoCommitTriggerLast": true, "sequential": false, "slots": null, "confidential": false, "dataloss": false, "dlq": false, "freshVariables": false}, "designer": {"orphans": []}, "zone": "eu2.make.com", "notes": []}}